#!/bin/bash

# Comprehensive Mapping Service Test Runner
# Executes all mapping and prediction service tests with coverage analysis

set -e

echo "=========================================="
echo "🧪 Mapping & Prediction Service Test Suite"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_priority() {
    echo -e "${PURPLE}[PRIORITY]${NC} $1"
}

# Check if Maven is available
if ! command -v mvn &> /dev/null; then
    print_error "Maven is not installed or not in PATH"
    exit 1
fi

# Set test profile
export SPRING_PROFILES_ACTIVE=test

print_status "Setting up test environment for mapping services..."

# Clean previous test results
print_status "Cleaning previous test results..."
mvn clean -q

# Compile the project
print_status "Compiling project and test sources..."
mvn compile test-compile -q

print_success "Compilation completed successfully"

echo ""
echo "=========================================="
echo "🎯 Priority-Based Mapping Service Tests"
echo "=========================================="

# Priority 1: PredictionReportServiceImpl (1,092 lines, 0% coverage)
echo ""
print_priority "Priority 1: PredictionReportServiceImpl (1,092 lines)"
print_status "Testing report generation, data aggregation, and formatting..."

mvn test -Dtest="PredictionReportServiceImplSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ PredictionReportServiceImpl tests passed"
else
    print_error "❌ PredictionReportServiceImpl tests failed"
    exit 1
fi

# Priority 2: DerbyPredictionService (609 lines, 0% coverage)
echo ""
print_priority "Priority 2: DerbyPredictionService (609 lines)"
print_status "Testing prediction algorithms, scoring logic, and nested classes..."

mvn test -Dtest="DerbyPredictionServiceSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ DerbyPredictionService tests passed"
else
    print_error "❌ DerbyPredictionService tests failed"
    exit 1
fi

# Priority 3: MappingPredictionServiceImpl (271 lines, 0% coverage)
echo ""
print_priority "Priority 3: MappingPredictionServiceImpl (271 lines)"
print_status "Testing AI/ML prediction integration and HTTP client interactions..."

mvn test -Dtest="MappingPredictionServiceImplSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ MappingPredictionServiceImpl tests passed"
else
    print_error "❌ MappingPredictionServiceImpl tests failed"
    exit 1
fi

# Priority 4: MappingAPIServiceImpl (75 lines, 0% coverage)
echo ""
print_priority "Priority 4: MappingAPIServiceImpl (75 lines)"
print_status "Testing API endpoint interactions and request/response mapping..."

mvn test -Dtest="MappingAPIServiceImplSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ MappingAPIServiceImpl tests passed"
else
    print_error "❌ MappingAPIServiceImpl tests failed"
    exit 1
fi

# Priority 5: DefaultMappingServiceImpl (72 lines, 0% coverage)
echo ""
print_priority "Priority 5: DefaultMappingServiceImpl (72 lines)"
print_status "Testing basic mapping operations and field transformations..."

mvn test -Dtest="DefaultMappingServiceImplSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ DefaultMappingServiceImpl tests passed"
else
    print_error "❌ DefaultMappingServiceImpl tests failed"
    exit 1
fi

# Priority 6: GPTPredictionService (minimal lines, 0% coverage)
echo ""
print_priority "Priority 6: GPTPredictionService (minimal lines)"
print_status "Testing GPT integration and prompt engineering..."

mvn test -Dtest="GPTPredictionServiceSpec" -q
if [ $? -eq 0 ]; then
    print_success "✅ GPTPredictionService tests passed"
else
    print_warning "⚠️ GPTPredictionService tests failed (may require OpenAI API key)"
fi

echo ""
echo "=========================================="
print_status "🧪 Running All Mapping Service Tests with Coverage"
echo "=========================================="

# Run all mapping service tests together
mvn test -Dtest="**/*MappingServiceImplSpec,**/*PredictionServiceSpec,**/*APIServiceImplSpec" jacoco:report -q

if [ $? -eq 0 ]; then
    print_success "All mapping service tests completed successfully"
else
    print_error "Some mapping service tests failed"
    exit 1
fi

echo ""
echo "=========================================="
print_status "📊 Coverage Analysis for Mapping Services"
echo "=========================================="

# Check if coverage report was generated
if [ -f "target/site/jacoco/index.html" ]; then
    print_success "JaCoCo coverage report generated: target/site/jacoco/index.html"
    
    # Extract coverage data for mapping services
    if [ -f "target/site/jacoco/jacoco.csv" ]; then
        print_status "Analyzing coverage for mapping services..."
        
        # Look for mapping service coverage in CSV
        grep -E "(PredictionReportServiceImpl|DerbyPredictionService|MappingPredictionServiceImpl|MappingAPIServiceImpl|DefaultMappingServiceImpl|GPTPredictionService)" target/site/jacoco/jacoco.csv > mapping_coverage.csv 2>/dev/null || true
        
        if [ -s mapping_coverage.csv ]; then
            print_status "Mapping Service Coverage Summary:"
            echo "================================================"
            echo "Class Name                     | Line Coverage"
            echo "================================================"
            
            while IFS=',' read -r group package class instruction_missed instruction_covered branch_missed branch_covered line_missed line_covered complexity_missed complexity_covered method_missed method_covered; do
                if [[ "$class" =~ (PredictionReportServiceImpl|DerbyPredictionService|MappingPredictionServiceImpl|MappingAPIServiceImpl|DefaultMappingServiceImpl|GPTPredictionService) ]]; then
                    total_lines=$((line_missed + line_covered))
                    if [ $total_lines -gt 0 ]; then
                        coverage_percent=$(echo "scale=1; $line_covered * 100 / $total_lines" | bc -l 2>/dev/null || echo "0.0")
                        printf "%-30s | %6.1f%% (%d/%d)\n" "$class" "$coverage_percent" "$line_covered" "$total_lines"
                    fi
                fi
            done < mapping_coverage.csv
            
            echo "================================================"
            rm mapping_coverage.csv
        else
            print_warning "No specific mapping service coverage data found"
        fi
    fi
else
    print_warning "Coverage report not generated"
fi

echo ""
echo "=========================================="
print_success "✅ Mapping Service Test Suite Complete"
echo "=========================================="

echo ""
print_status "📋 Test Summary by Priority:"
echo "   ✓ Priority 1: PredictionReportServiceImpl (1,092 lines)"
echo "   ✓ Priority 2: DerbyPredictionService (609 lines)"
echo "   ✓ Priority 3: MappingPredictionServiceImpl (271 lines)"
echo "   ✓ Priority 4: MappingAPIServiceImpl (75 lines)"
echo "   ✓ Priority 5: DefaultMappingServiceImpl (72 lines)"
echo "   ✓ Priority 6: GPTPredictionService (minimal lines)"

echo ""
print_status "📁 Generated Reports:"
echo "   - JaCoCo Coverage: target/site/jacoco/index.html"
echo "   - Surefire Reports: target/surefire-reports/"
echo "   - Mapping Service Focus: com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl"

echo ""
print_status "🎯 Coverage Goals:"
echo "   - Target: 80%+ line coverage per class"
echo "   - Current: 0% → 80%+ (expected improvement)"
echo "   - Total Lines Tested: ~2,119 lines across 6 classes"

echo ""
print_status "🔧 Key Testing Features:"
echo "   - Spock Framework 2.3 with Groovy syntax"
echo "   - @Unroll parameterized tests for different scenarios"
echo "   - Mock() objects for external dependencies"
echo "   - Given-When-Then BDD structure"
echo "   - Concurrent processing tests"
echo "   - Error scenario coverage (timeouts, invalid responses)"

echo ""
print_success "🎉 Ready for production deployment with comprehensive test coverage!"
