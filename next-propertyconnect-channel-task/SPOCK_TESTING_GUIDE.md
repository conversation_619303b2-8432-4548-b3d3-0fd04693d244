# 🧪 Comprehensive Spock Testing Guide

## 📋 Overview

This guide implements a **5-Phase Spock Testing Plan** to raise the line coverage of the `com.derbysoft.next.propertyconnect.channel.task` package from ~29% to ≥80% using the Spock Framework with Groovy.

## 🏗️ Architecture

### Framework Stack
- **Spock Framework 2.3** - BDD testing framework
- **Groovy 4.0** - Dynamic language for test scripts
- **Spring Boot Test** - Integration testing support
- **JaCoCo** - Code coverage reporting
- **Maven** - Build and dependency management

### Test Structure
```
src/test/groovy/
└── com/derbysoft/next/propertyconnect/channel/task/
    ├── service/mapping/impl/          # Phase 1: Mapping Tests
    ├── service/impl/                  # Phase 2: Service Tests  
    ├── controller/api/                # Phase 3: Controller Tests
    ├── domain/dto/                    # Phase 4: DTO/Entity Tests
    └── integration/                   # Phase 5: Integration Tests
```

## 🚀 5-Phase Testing Plan

### **Phase 1 (Week 1) – Mapping & Utility Tests**
**Target**: `service.mapping.impl` (MapStruct/手写映射)  
**Focus**: 空输入、全字段映射、错误值、null 安全  
**Technology**: Spock `Specification` + Groovy `@Shared`/`@Unroll` 参数化

**Test Files:**
- `DefaultMappingServiceImplSpec.groovy`
- `MappingPredictionServiceImplSpec.groovy`
- `BusinessJSONUtilSpec.groovy`

**Key Features:**
```groovy
@Unroll
def "should handle null input gracefully for method: #methodName"() {
    when: "calling method with null input"
    def result = mappingService."$methodName"(null)
    
    then: "should return null or empty result without throwing exception"
    result == null || result == [] || result == [:]
    
    where:
    methodName << ['mapProducts', 'mapChannelProducts', 'transformProductData']
}
```

### **Phase 2 (Week 2) – 外部服务逻辑测试**
**Target**: `service.impl.agoda`、`service.impl.bookingcom` 等  
**Focus**: 正常返回、接口超时、异常重试、限流降级  
**Mock**: Spock 的 `Mock()` 模拟 HTTP 客户端

**Test Files:**
- `AgodaServiceImplSpec.groovy`
- `BookingcomServiceImplSpec.groovy`

**Key Features:**
```groovy
def "should handle HTTP client timeout"() {
    given: "mock client throws timeout exception"
    mockAgodaClient.getRooms(_) >> { 
        throw new RetryableException(500, "Connection timeout", null, null, null)
    }
    
    when: "calling service method"
    def result = agodaService.getRooms(request)
    
    then: "should handle timeout gracefully"
    thrown(RetryableException)
    1 * mockAgodaClient.getRooms(_)
}
```

### **Phase 3 (Week 3) – 控制层测试**
**Target**: REST Controller  
**Technology**: `@WebMvcTest` + `@AutoConfigureMockMvc` + Spock  
**Scenarios**: 参数校验失败 → 400, 资源不存在 → 404, 服务异常 → 500

**Test Files:**
- `ChannelHotelControllerSpec.groovy`

**Key Features:**
```groovy
@WebMvcTest
@ContextConfiguration(classes = [TestConfig])
class ChannelHotelControllerSpec extends Specification {
    
    @Autowired
    MockMvc mockMvc
    
    def "should return 400 for invalid channel hotel request"() {
        when: "posting invalid request"
        def response = mockMvc.perform(post("/api/channel-hotels")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
        
        then: "should return 400 Bad Request"
        response.andExpect(status().isBadRequest())
    }
}
```

### **Phase 4 (Week 4) – 数据层 & DTO/Entity 测试**
**Technology**: `@DataJpaTest` + H2 + Spock  
**Scenarios**: CRUD、分页查询、异常场景、`equals`/`hashCode`、序列化/反序列化

**Test Files:**
- `ChannelProductsDTOSpec.groovy`

**Key Features:**
```groovy
def "should implement equals and hashCode correctly for Product"() {
    given: "two identical products"
    def product1 = new ChannelProductsDTO.Product().tap { /* ... */ }
    def product2 = new ChannelProductsDTO.Product().tap { /* ... */ }
    
    expect: "equals and hashCode should work correctly"
    product1.equals(product2)
    product1.hashCode() == product2.hashCode()
}
```

### **Phase 5 (Week 5) – 全上下文集成测试**
**Technology**: `@SpringBootTest` + Spock  
**Scenarios**: 端到端业务流、定时任务触发与失败回退

**Test Files:**
- `ChannelTaskIntegrationSpec.groovy`

**Key Features:**
```groovy
@SpringBootTest(classes = ChannelTaskApplication.class)
@ActiveProfiles("test")
@Stepwise
class ChannelTaskIntegrationSpec extends Specification {
    
    def "should process channel products workflow"() {
        given: "channel products data"
        // ... setup
        
        when: "processing channel products"
        def processedProducts = channelProductsService.processChannelProducts(channelProductsDTO)
        
        then: "products should be processed successfully"
        processedProducts != null
        processedProducts.channelProducts.size() == 2
    }
}
```

## 🛠️ Setup & Configuration

### 1. Maven Dependencies
```xml
<!-- Spock Framework -->
<dependency>
    <groupId>org.spockframework</groupId>
    <artifactId>spock-core</artifactId>
    <version>2.3-groovy-4.0</version>
    <scope>test</scope>
</dependency>
<dependency>
    <groupId>org.spockframework</groupId>
    <artifactId>spock-spring</artifactId>
    <version>2.3-groovy-4.0</version>
    <scope>test</scope>
</dependency>
```

### 2. Groovy Compiler Plugin
```xml
<plugin>
    <groupId>org.codehaus.gmavenplus</groupId>
    <artifactId>gmavenplus-plugin</artifactId>
    <version>3.0.2</version>
    <executions>
        <execution>
            <goals>
                <goal>compileTests</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

## 🏃‍♂️ Running Tests

### Quick Start
```bash
# Make script executable
chmod +x run_spock_tests.sh

# Run all phases
./run_spock_tests.sh
```

### Individual Phases
```bash
# Phase 1: Mapping Tests
mvn test -Dtest="**/*MappingServiceImplSpec,**/*BusinessJSONUtilSpec"

# Phase 2: Service Tests  
mvn test -Dtest="**/*AgodaServiceImplSpec,**/*BookingcomServiceImplSpec"

# Phase 3: Controller Tests
mvn test -Dtest="**/*ControllerSpec"

# Phase 4: DTO/Entity Tests
mvn test -Dtest="**/*DTOSpec,**/*EntitySpec"

# Phase 5: Integration Tests
mvn test -Dtest="**/*IntegrationSpec"
```

### Coverage Report
```bash
# Generate JaCoCo coverage report
mvn test jacoco:report

# View report
open target/site/jacoco/index.html
```

## 📊 Coverage Goals

### Target Metrics
- **Line Coverage**: ≥80%
- **Branch Coverage**: ≥70%
- **Method Coverage**: ≥85%

### Exclusions
- MapStruct generated classes (`*Mapper*Impl`, `*Mapper*$*`)
- Configuration classes
- Application main class

## 🎯 Key Spock Features Used

### 1. Parameterized Tests with @Unroll
```groovy
@Unroll
def "should validate field constraints: #fieldName with value #fieldValue"() {
    // Test implementation
    where:
    fieldName       | fieldValue | expectedValue
    "maxOccupancy"  | -1         | -1
    "maxOccupancy"  | 0          | 0
    "maxOccupancy"  | 10         | 10
}
```

### 2. Mock Objects
```groovy
AccountSettingService mockAccountSettingService = Mock()
AgodaClient mockAgodaClient = Mock()

// Stubbing
mockAccountSettingService.getAccountSettings(_) >> [username: "user", password: "pass"]

// Verification
1 * mockAgodaClient.getRooms(_)
```

### 3. Given-When-Then Structure
```groovy
def "should create Agoda request successfully"() {
    given: "a hotel ID"
    def hotelId = "TEST_HOTEL_001"
    
    and: "mock account settings"
    mockAccountSettingService.getAccountSettings(hotelId) >> [/* ... */]
    
    when: "creating Agoda request"
    def result = agodaService.createAgodaRequest(hotelId)
    
    then: "should return valid request object"
    result != null
    result.hotelId == hotelId
}
```

### 4. Shared Resources
```groovy
@Shared
DefaultMappingServiceImpl mappingService

def setupSpec() {
    mappingService = new DefaultMappingServiceImpl()
}
```

## 🔧 Best Practices

### 1. Test Naming
- Use descriptive method names: `should create channel hotel successfully`
- Follow BDD style: `given-when-then`

### 2. Mock Usage
- Use `Mock()` for behavior verification
- Use `Stub()` for simple return values
- Use `Spy()` for partial mocking

### 3. Data Setup
- Use `@Shared` for expensive setup
- Use `setup()` for per-test initialization
- Use `cleanup()` for resource cleanup

### 4. Assertions
- Prefer Groovy truth over explicit assertions
- Use `with()` for multiple assertions on same object
- Use `thrown()` for exception testing

## 📈 Expected Results

After implementing all 5 phases:
- **Current Coverage**: ~29%
- **Target Coverage**: ≥80%
- **Test Count**: 100+ comprehensive tests
- **Mock Coverage**: All external dependencies
- **Integration Coverage**: End-to-end workflows

## 🎉 Success Metrics

✅ **Phase 1**: Mapping & utility classes at 90%+ coverage  
✅ **Phase 2**: Service implementations at 85%+ coverage  
✅ **Phase 3**: Controllers at 80%+ coverage  
✅ **Phase 4**: DTOs/Entities at 95%+ coverage  
✅ **Phase 5**: Integration workflows validated  

**Overall Goal**: Package coverage from 29% → 80%+ 🚀
