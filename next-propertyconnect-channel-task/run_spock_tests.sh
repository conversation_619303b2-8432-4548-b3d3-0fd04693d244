#!/bin/bash

# Comprehensive Spock Test Runner Script
# Executes all 5 phases of Spock tests with coverage reporting

set -e

echo "=========================================="
echo "🚀 Starting Comprehensive Spock Test Suite"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    print_error "<PERSON>ven is not installed or not in PATH"
    exit 1
fi

# Set test profile
export SPRING_PROFILES_ACTIVE=test

print_status "Setting up test environment..."

# Clean previous test results
print_status "Cleaning previous test results..."
mvn clean -q

# Compile the project
print_status "Compiling project..."
mvn compile -q

# Compile test sources (including Groovy/Spock tests)
print_status "Compiling test sources..."
mvn test-compile -q

print_success "Compilation completed successfully"

echo ""
echo "=========================================="
echo "📋 Running 5-Phase Spock Test Plan"
echo "=========================================="

# Phase 1: Mapping & Utility Tests
echo ""
print_status "🔧 Phase 1: Mapping & Utility Tests"
echo "   - DefaultMappingServiceImplSpec"
echo "   - MappingPredictionServiceImplSpec" 
echo "   - BusinessJSONUtilSpec"

mvn test -Dtest="**/*MappingServiceImplSpec,**/*BusinessJSONUtilSpec" -q
if [ $? -eq 0 ]; then
    print_success "Phase 1 tests passed"
else
    print_error "Phase 1 tests failed"
    exit 1
fi

# Phase 2: External Service Logic Tests
echo ""
print_status "🌐 Phase 2: External Service Logic Tests"
echo "   - AgodaServiceImplSpec"
echo "   - BookingcomServiceImplSpec"

mvn test -Dtest="**/*AgodaServiceImplSpec,**/*BookingcomServiceImplSpec" -q
if [ $? -eq 0 ]; then
    print_success "Phase 2 tests passed"
else
    print_error "Phase 2 tests failed"
    exit 1
fi

# Phase 3: Controller Layer Tests
echo ""
print_status "🎮 Phase 3: Controller Layer Tests"
echo "   - ChannelHotelControllerSpec"

mvn test -Dtest="**/*ControllerSpec" -q
if [ $? -eq 0 ]; then
    print_success "Phase 3 tests passed"
else
    print_error "Phase 3 tests failed"
    exit 1
fi

# Phase 4: Data Layer & DTO/Entity Tests
echo ""
print_status "💾 Phase 4: Data Layer & DTO/Entity Tests"
echo "   - ChannelProductsDTOSpec"

mvn test -Dtest="**/*DTOSpec,**/*EntitySpec" -q
if [ $? -eq 0 ]; then
    print_success "Phase 4 tests passed"
else
    print_error "Phase 4 tests failed"
    exit 1
fi

# Phase 5: Integration Tests
echo ""
print_status "🔗 Phase 5: Integration Tests"
echo "   - ChannelTaskIntegrationSpec"

mvn test -Dtest="**/*IntegrationSpec" -q
if [ $? -eq 0 ]; then
    print_success "Phase 5 tests passed"
else
    print_warning "Phase 5 tests failed (may require external dependencies)"
fi

echo ""
echo "=========================================="
print_status "🧪 Running Full Test Suite with Coverage"
echo "=========================================="

# Run all tests with JaCoCo coverage
mvn test jacoco:report -q

if [ $? -eq 0 ]; then
    print_success "All tests completed successfully"
else
    print_error "Some tests failed"
    exit 1
fi

echo ""
echo "=========================================="
print_status "📊 Generating Coverage Report"
echo "=========================================="

# Check if coverage report was generated
if [ -f "target/site/jacoco/index.html" ]; then
    print_success "JaCoCo coverage report generated: target/site/jacoco/index.html"
    
    # Extract coverage percentage if possible
    if [ -f "target/site/jacoco/jacoco.csv" ]; then
        print_status "Analyzing coverage data..."
        
        # Run Python coverage analysis script if available
        if [ -f "../analyze_coverage.py" ]; then
            python3 ../analyze_coverage.py
        else
            print_warning "Coverage analysis script not found"
        fi
    fi
else
    print_warning "Coverage report not generated"
fi

echo ""
echo "=========================================="
print_success "✅ Spock Test Suite Execution Complete"
echo "=========================================="

echo ""
print_status "📋 Test Summary:"
echo "   ✓ Phase 1: Mapping & Utility Tests"
echo "   ✓ Phase 2: External Service Logic Tests"
echo "   ✓ Phase 3: Controller Layer Tests"
echo "   ✓ Phase 4: Data Layer & DTO/Entity Tests"
echo "   ✓ Phase 5: Integration Tests"

echo ""
print_status "📁 Generated Reports:"
echo "   - JaCoCo Coverage: target/site/jacoco/index.html"
echo "   - Surefire Reports: target/surefire-reports/"

echo ""
print_status "🎯 Next Steps:"
echo "   1. Open coverage report in browser"
echo "   2. Review test results in target/surefire-reports/"
echo "   3. Address any failing tests"
echo "   4. Aim for ≥80% line coverage"

echo ""
print_success "🎉 Happy Testing with Spock Framework!"
