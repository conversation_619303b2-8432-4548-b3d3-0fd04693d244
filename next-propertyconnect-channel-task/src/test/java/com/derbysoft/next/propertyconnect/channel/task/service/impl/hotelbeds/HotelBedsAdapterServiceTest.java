package com.derbysoft.next.propertyconnect.channel.task.service.impl.hotelbeds;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.InventoryItemStatus;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.SyncStatus;
import com.derbysoft.next.propertyconnect.channel.task.service.storageservice.ChannelHotelStorageService;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for HotelBedsAdapterService
 * Tests channel hotel activation customization logic
 */
@ExtendWith(MockitoExtension.class)
class HotelBedsAdapterServiceTest {

    @Mock
    private HotelBedsConfigProperties hotelBedsConfigProperties;

    @Mock
    private HotelBedsClient hotelBedsClient;

    @Mock
    private ChannelHotelStorageService channelHotelStorageService;

    @Mock
    private PerfLogHandler perfLogHandler;

    @InjectMocks
    private HotelBedsAdapterService hotelBedsAdapterService;

    private ChannelHotelDTO testChannelHotelDTO;
    private InventoryItemStatus testHotelInfo;

    @BeforeEach
    void setUp() {
        testHotelInfo = new InventoryItemStatus();
        testHotelInfo.setCode("TEST_HOTEL");
        testHotelInfo.setSyncStatus(SyncStatus.PENDING);
        testHotelInfo.setExtensions(new HashMap<>());

        testChannelHotelDTO = new ChannelHotelDTO();
        testChannelHotelDTO.setChannelId("HOTELBEDS");
        testChannelHotelDTO.setChannelHotelId("TEST_HOTEL_ID");
        testChannelHotelDTO.setSupplierId("TEST_SUPPLIER");
        testChannelHotelDTO.setHotelInfo(testHotelInfo);
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // When
        String result = hotelBedsAdapterService.channel();

        // Then
        assertEquals("HOTELBEDS", result, "Should return HOTELBEDS channel name");
    }

    @Test
    void testSaveChannelHotel_ReturnsTrue() {
        // When
        boolean result = hotelBedsAdapterService.saveChannelHotel();

        // Then
        assertTrue(result, "Should return true for saveChannelHotel");
    }

    @Test
    void testCustomizeProperty_WithPropertyToChannelEnabled_AndNotSynced_CallsSetupProperty() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        
        InventoryItemStatus existingHotel = new InventoryItemStatus();
        existingHotel.setSyncStatus(SyncStatus.PENDING);
        ChannelHotelDTO existingChannelHotel = new ChannelHotelDTO();
        existingChannelHotel.setHotelInfo(existingHotel);
        
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(existingChannelHotel);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient).setupProperty(any(HotelBedsPropertySetupRQ.class));
            verify(perfLogHandler).messages("ext_setup_to_channel", "Success");
            assertEquals(SyncStatus.SYNCED, testHotelInfo.getSyncStatus());
        }
    }

    @Test
    void testCustomizeProperty_WithPropertyToChannelEnabled_AndAlreadySynced_SkipsSetup() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        
        InventoryItemStatus existingHotel = new InventoryItemStatus();
        existingHotel.setSyncStatus(SyncStatus.SYNCED);
        ChannelHotelDTO existingChannelHotel = new ChannelHotelDTO();
        existingChannelHotel.setHotelInfo(existingHotel);
        
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(existingChannelHotel);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient, never()).setupProperty(any());
            verify(perfLogHandler).messages("ext_setup_to_channel", "Exist");
            assertEquals(SyncStatus.SYNCED, testHotelInfo.getSyncStatus());
        }
    }

    @Test
    void testCustomizeProperty_WithPropertyToChannelDisabled_SetsExtensions() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(false);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient, never()).setupProperty(any());
            verify(perfLogHandler).messages("ext_setup_to_channel", "Ignore");
            assertEquals("PMSCON", testHotelInfo.getExtensions().get("channelProviderCode"));
        }
    }

    @Test
    void testCustomizeProperty_WithFeignException_HandlesFail() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        
        InventoryItemStatus existingHotel = new InventoryItemStatus();
        existingHotel.setSyncStatus(SyncStatus.PENDING);
        ChannelHotelDTO existingChannelHotel = new ChannelHotelDTO();
        existingChannelHotel.setHotelInfo(existingHotel);
        
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(existingChannelHotel);

        FeignException feignException = mock(FeignException.class);
        when(feignException.contentUTF8()).thenReturn("Setup failed");
        when(hotelBedsClient.setupProperty(any())).thenThrow(feignException);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(perfLogHandler).messages("ext_setup_to_channel", "Fail");
            verify(perfLogHandler).messages("ext_setup_to_failed_reason", "Setup failed");
            assertEquals(SyncStatus.SYNC_FAILED, testHotelInfo.getSyncStatus());
            assertEquals("Setup failed", testHotelInfo.getErrorMessage());
        }
    }

    @Test
    void testCustomizeProperty_WithNullExistingHotel_CallsSetupProperty() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(null);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient).setupProperty(any(HotelBedsPropertySetupRQ.class));
            verify(perfLogHandler).messages("ext_setup_to_channel", "Success");
            assertEquals(SyncStatus.SYNCED, testHotelInfo.getSyncStatus());
        }
    }

    @Test
    void testCustomizeProperty_WithNullHotelInfo_CallsSetupProperty() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        
        ChannelHotelDTO existingChannelHotel = new ChannelHotelDTO();
        existingChannelHotel.setHotelInfo(null);
        
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(existingChannelHotel);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient).setupProperty(any(HotelBedsPropertySetupRQ.class));
            verify(perfLogHandler).messages("ext_setup_to_channel", "Success");
            assertEquals(SyncStatus.SYNCED, testHotelInfo.getSyncStatus());
        }
    }

    @Test
    void testCustomizeProperty_WithPropertyToChannelNull_TreatedAsFalse() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(null);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient, never()).setupProperty(any());
            verify(perfLogHandler).messages("ext_setup_to_channel", "Ignore");
            assertEquals("PMSCON", testHotelInfo.getExtensions().get("channelProviderCode"));
        }
    }

    @Test
    void testCustomizeProperty_WithExistingExtensions_PreservesOtherExtensions() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(false);
        testHotelInfo.getExtensions().put("existingKey", "existingValue");

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            Map<String, Object> extensions = testHotelInfo.getExtensions();
            assertEquals("PMSCON", extensions.get("channelProviderCode"));
            assertEquals("existingValue", extensions.get("existingKey"));
        }
    }

    @Test
    void testCustomizeProperty_VerifiesCorrectSetupRequest() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(null);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient).setupProperty(argThat(request -> 
                "TEST_SUPPLIER".equals(request.getSupplierId()) &&
                "TEST_HOTEL_ID".equals(request.getChannelHotelId())
            ));
        }
    }

    @Test
    void testCustomizeProperty_WithMultipleCalls_HandlesCorrectly() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(false);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(perfLogHandler, times(2)).messages("ext_setup_to_channel", "Ignore");
            assertEquals("PMSCON", testHotelInfo.getExtensions().get("channelProviderCode"));
        }
    }

    @Test
    void testCustomizeProperty_WithDifferentSyncStatuses_HandlesCorrectly() {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(true);

        // Test with SYNC_FAILED status
        InventoryItemStatus existingHotel = new InventoryItemStatus();
        existingHotel.setSyncStatus(SyncStatus.SYNC_FAILED);
        ChannelHotelDTO existingChannelHotel = new ChannelHotelDTO();
        existingChannelHotel.setHotelInfo(existingHotel);
        
        when(channelHotelStorageService.getChannelHotel("HOTELBEDS", "TEST_HOTEL_ID"))
                .thenReturn(existingChannelHotel);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            hotelBedsAdapterService.customizeProperty(testChannelHotelDTO);

            // Then
            verify(hotelBedsClient).setupProperty(any(HotelBedsPropertySetupRQ.class));
            verify(perfLogHandler).messages("ext_setup_to_channel", "Success");
            assertEquals(SyncStatus.SYNCED, testHotelInfo.getSyncStatus());
        }
    }

    @Test
    void testCustomizeProperty_ThreadSafety_HandlesMultipleThreads() throws InterruptedException {
        // Given
        when(hotelBedsConfigProperties.getPropertyToChannel()).thenReturn(false);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            Thread[] threads = new Thread[5];
            for (int i = 0; i < 5; i++) {
                threads[i] = new Thread(() -> {
                    ChannelHotelDTO dto = new ChannelHotelDTO();
                    InventoryItemStatus hotelInfo = new InventoryItemStatus();
                    hotelInfo.setExtensions(new HashMap<>());
                    dto.setHotelInfo(hotelInfo);
                    hotelBedsAdapterService.customizeProperty(dto);
                });
                threads[i].start();
            }

            for (Thread thread : threads) {
                thread.join();
            }

            // Then
            verify(perfLogHandler, times(5)).messages("ext_setup_to_channel", "Ignore");
        }
    }
}
