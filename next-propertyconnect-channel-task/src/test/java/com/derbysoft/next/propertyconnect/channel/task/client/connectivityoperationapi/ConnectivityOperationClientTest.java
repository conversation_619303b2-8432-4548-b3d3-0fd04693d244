package com.derbysoft.next.propertyconnect.channel.task.client.connectivityoperationapi;

import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for ConnectivityOperationClient
 * Tests all client methods and their parameter handling
 */
@ExtendWith(MockitoExtension.class)
class ConnectivityOperationClientTest {

    @Mock
    private ConnectivityOperationClient connectivityOperationClient;

    @Test
    void testConnectivityOperationClient_IsInterface_HasCorrectMethods() {
        // Given
        Class<ConnectivityOperationClient> clientClass = ConnectivityOperationClient.class;

        // When & Then
        assertTrue(clientClass.isInterface(), "ConnectivityOperationClient should be an interface");
        assertTrue(clientClass.getDeclaredMethods().length > 0, "Should have methods declared");
    }

    @Test
    void testGetCredential_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.getCredential(channelId, channelHotelId, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.getCredential(channelId, channelHotelId, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).getCredential(channelId, channelHotelId, traceToken);
    }

    @Test
    void testSaveCredential_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("username", "testuser");
        request.put("password", "testpass");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.saveCredential(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.saveCredential(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).saveCredential(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testGetProperty_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.getProperty(channelId, channelHotelId, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.getProperty(channelId, channelHotelId, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).getProperty(channelId, channelHotelId, traceToken);
    }

    @Test
    void testSaveProperty_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("propertyName", "Test Hotel");
        request.put("address", "123 Test St");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.saveProperty(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.saveProperty(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).saveProperty(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testGetRoomType_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.getRoomType(channelId, channelHotelId, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.getRoomType(channelId, channelHotelId, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).getRoomType(channelId, channelHotelId, traceToken);
    }

    @Test
    void testSaveRoomTypes_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("roomTypes", "test room types");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.saveRoomTypes(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.saveRoomTypes(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).saveRoomTypes(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testGetRatePlan_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.getRatePlan(channelId, channelHotelId, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.getRatePlan(channelId, channelHotelId, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).getRatePlan(channelId, channelHotelId, traceToken);
    }

    @Test
    void testSaveRatePlans_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("ratePlans", "test rate plans");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.saveRatePlans(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.saveRatePlans(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).saveRatePlans(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testGetProduct_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.getProduct(channelId, channelHotelId, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.getProduct(channelId, channelHotelId, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).getProduct(channelId, channelHotelId, traceToken);
    }

    @Test
    void testSaveProducts_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("products", "test products");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.saveProducts(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.saveProducts(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).saveProducts(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testTriggerARIRefresh_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("refreshType", "FULL");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.triggerARIRefresh(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.triggerARIRefresh(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).triggerARIRefresh(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testPropertyActivation_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("activationType", "FULL");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.propertyActivation(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.propertyActivation(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).propertyActivation(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testRoomTypeActivation_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("roomTypeActivation", "ACTIVE");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.roomTypeActivation(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.roomTypeActivation(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).roomTypeActivation(channelId, channelHotelId, request, traceToken);
    }

    @Test
    void testRatePlanActivation_WithValidParameters_ReturnsResponse() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        JSONObject request = new JSONObject();
        request.put("ratePlanActivation", "ACTIVE");
        String traceToken = "TEST_TOKEN";
        ResponseEntity<JSONObject> expectedResponse = ResponseEntity.ok(new JSONObject());
        
        when(connectivityOperationClient.ratePlanActivation(channelId, channelHotelId, request, traceToken))
                .thenReturn(expectedResponse);

        // When
        ResponseEntity<JSONObject> result = connectivityOperationClient.ratePlanActivation(channelId, channelHotelId, request, traceToken);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedResponse, result, "Should return expected response");
        verify(connectivityOperationClient).ratePlanActivation(channelId, channelHotelId, request, traceToken);
    }
}
