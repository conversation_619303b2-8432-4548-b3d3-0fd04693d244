package com.derbysoft.next.propertyconnect.channel.task.service.impl.synxis;

import com.alibaba.fastjson2.JSONObject;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.AccountSettingService;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for SynxisServiceImpl
 * Tests Synxis channel service implementation
 */
@ExtendWith(MockitoExtension.class)
class SynxisServiceImplTest {

    @Mock
    private SynxisClient synxisClient;

    @Mock
    private AccountSettingService accountSettingService;

    @Mock
    private RemoteService remoteService;

    @Mock
    private SynxisConfigProperties configProperties;

    @Mock
    private SynxisAdapterClient synxisAdapterClient;

    private SynxisServiceImpl synxisService;

    private ChannelProductsDTO testChannelProductsDTO;

    @BeforeEach
    void setUp() {
        synxisService = new SynxisServiceImpl(
                synxisClient,
                accountSettingService,
                remoteService,
                configProperties,
                synxisAdapterClient
        );

        testChannelProductsDTO = new ChannelProductsDTO();
        testChannelProductsDTO.setHotelId("TEST_HOTEL_ID");
        testChannelProductsDTO.setChannelProducts(new ArrayList<>());
    }

    @Test
    void testChannel_ReturnsCorrectChannelName() {
        // When
        String result = synxisService.channel();

        // Then
        assertEquals("SYNXISDISTRIBUTOR", result, "Should return SYNXISDISTRIBUTOR channel name");
    }

    @Test
    void testSyncRatePlans_WithValidInput_ReturnsSuccessfulResult() {
        // Given
        String channelHotelId = "TEST_CHANNEL_HOTEL";
        String adapterResponse = "valid_adapter_response";
        
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn(adapterResponse);
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(createMockChannelRates());
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(createMockAccountSetting());
        
        String mockSoapResponse = createMockSoapResponse("TEST_ECHO_TOKEN");
        when(synxisClient.syncRatePlans(anyString(), anyString())).thenReturn(mockSoapResponse);

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", channelHotelId, testChannelProductsDTO);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(channelHotelId, result.getChannelHotelId(), "Channel hotel ID should match");
        assertEquals("SYNXISDISTRIBUTOR", result.getChannelId(), "Channel ID should match");
        assertNotNull(result.getChannelProducts(), "Channel products should not be null");
    }

    @Test
    void testSyncRatePlans_WithNullHotelId_ThrowsException() {
        // Given
        testChannelProductsDTO.setHotelId(null);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);
        }, "Should throw exception for null hotel ID");
    }

    @Test
    void testSyncRatePlans_WithEmptyAdapterResponse_ThrowsException() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);
        });
        
        assertTrue(exception.getMessage().contains("Hotel in adapter not found"), 
                "Exception message should indicate hotel not found");
    }

    @Test
    void testSyncRatePlans_WithNullAdapterResponse_ThrowsException() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);
        });
        
        assertTrue(exception.getMessage().contains("Hotel in adapter not found"), 
                "Exception message should indicate hotel not found");
    }

    @Test
    void testSyncRatePlans_VerifiesAdapterQuery() {
        // Given
        String channelHotelId = "TEST_CHANNEL_HOTEL";
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(createMockChannelRates());
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(createMockAccountSetting());
        
        String mockSoapResponse = createMockSoapResponse("TEST_ECHO_TOKEN");
        when(synxisClient.syncRatePlans(anyString(), anyString())).thenReturn(mockSoapResponse);

        // When
        synxisService.syncRatePlans("SYNXISDISTRIBUTOR", channelHotelId, testChannelProductsDTO);

        // Then
        verify(synxisAdapterClient).query(argThat(json -> {
            return "PROPERTYCONNECT".equals(json.getString("supplierCode")) &&
                   "TEST_HOTEL_ID".equals(json.getString("supplierHotelCode"));
        }));
    }

    @Test
    void testSyncRatePlans_WithEmptyChannelRates_ReturnsEmptyProducts() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(new ArrayList<>());

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);

        // Then
        assertNotNull(result.getChannelProducts(), "Channel products should not be null");
        assertTrue(result.getChannelProducts().isEmpty(), "Channel products should be empty");
    }

    @Test
    void testSyncRatePlans_WithNullAccountSetting_HandlesGracefully() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(createMockChannelRates());
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(null);

        // When & Then
        assertDoesNotThrow(() -> {
            synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);
        }, "Should handle null account setting gracefully");
    }

    @Test
    void testSyncRatePlans_InitializesChannelProductsDTO() {
        // Given
        String channelHotelId = "TEST_CHANNEL_HOTEL";
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(new ArrayList<>());

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", channelHotelId, testChannelProductsDTO);

        // Then
        assertEquals(channelHotelId, result.getChannelHotelId(), "Channel hotel ID should be set");
        assertEquals("SYNXISDISTRIBUTOR", result.getChannelId(), "Channel ID should be set");
        assertNotNull(result.getChannelProducts(), "Channel products should be initialized");
    }

    @Test
    void testSyncRatePlans_WithMultipleChannelRates_ProcessesAll() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        
        List<Map<String, Object>> channelRates = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> rate = new HashMap<>();
            rate.put("id", "RATE_" + i);
            rate.put("name", "Rate " + i);
            channelRates.add(rate);
        }
        
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(channelRates);
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(createMockAccountSetting());
        
        String mockSoapResponse = createMockSoapResponse("TEST_ECHO_TOKEN");
        when(synxisClient.syncRatePlans(anyString(), anyString())).thenReturn(mockSoapResponse);

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);

        // Then
        verify(synxisClient, times(3)).syncRatePlans(anyString(), anyString());
        assertNotNull(result.getChannelProducts(), "Channel products should not be null");
    }

    @Test
    void testConstructor_InitializesAllFields() {
        // Given & When
        SynxisServiceImpl service = new SynxisServiceImpl(
                synxisClient,
                accountSettingService,
                remoteService,
                configProperties,
                synxisAdapterClient
        );

        // Then
        assertNotNull(service, "Service should be created successfully");
        assertEquals("SYNXISDISTRIBUTOR", service.channel(), "Channel should be set correctly");
    }

    @Test
    void testSyncRatePlans_WithRuntimeException_HandlesGracefully() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(createMockChannelRates());
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(createMockAccountSetting());
        when(synxisClient.syncRatePlans(anyString(), anyString())).thenThrow(new RuntimeException("Network error"));

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);

        // Then
        assertNotNull(result, "Result should not be null even with exceptions");
        assertNotNull(result.getChannelProducts(), "Channel products should not be null");
    }

    @Test
    void testSyncRatePlans_WithInvalidSoapResponse_HandlesGracefully() {
        // Given
        when(synxisAdapterClient.query(any(JSONObject.class))).thenReturn("valid_response");
        when(remoteService.getChannelRates(anyString(), anyString())).thenReturn(createMockChannelRates());
        when(accountSettingService.getAccountSetting(anyString(), anyString())).thenReturn(createMockAccountSetting());
        when(synxisClient.syncRatePlans(anyString(), anyString())).thenReturn("invalid_xml");

        // When
        ChannelProductsDTO result = synxisService.syncRatePlans("SYNXISDISTRIBUTOR", "TEST_CHANNEL_HOTEL", testChannelProductsDTO);

        // Then
        assertNotNull(result, "Result should not be null");
        assertNotNull(result.getChannelProducts(), "Channel products should not be null");
    }

    // Helper methods
    private List<Map<String, Object>> createMockChannelRates() {
        List<Map<String, Object>> rates = new ArrayList<>();
        Map<String, Object> rate = new HashMap<>();
        rate.put("id", "TEST_RATE_ID");
        rate.put("name", "Test Rate");
        rates.add(rate);
        return rates;
    }

    private Map<String, Object> createMockAccountSetting() {
        Map<String, Object> setting = new HashMap<>();
        setting.put("username", "test_user");
        setting.put("password", "test_password");
        setting.put("endpoint", "test_endpoint");
        return setting;
    }

    private String createMockSoapResponse(String echoToken) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
               "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\">" +
               "<soap:Header>" +
               "<RelatesTo>" + echoToken + "</RelatesTo>" +
               "</soap:Header>" +
               "<soap:Body>" +
               "<Response>Success</Response>" +
               "</soap:Body>" +
               "</soap:Envelope>";
    }
}
