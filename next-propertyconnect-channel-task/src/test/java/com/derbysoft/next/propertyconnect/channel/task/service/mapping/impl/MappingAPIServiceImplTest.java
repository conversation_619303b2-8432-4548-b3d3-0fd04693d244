package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.commons.core.logsupport.handler.PerfLogHandler;
import com.derbysoft.next.propertyconenct.channel.common.exception.response.UnifyResult;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.MappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionMappingVO;
import com.derbysoft.next.propertyconnect.channel.task.controller.vo.mapping.PredictionModel;
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingPredictionService;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for MappingAPIServiceImpl
 * Tests mapping API service implementation
 */
@ExtendWith(MockitoExtension.class)
class MappingAPIServiceImplTest {

    @Mock
    private MappingService mappingService;

    @Mock
    private MappingPredictionService mappingPredictionService;

    @Mock
    private MappingAPIServiceImpl.MappingVOTranslator mappingVOTranslator;

    @Mock
    private MappingAPIServiceImpl.PredictionMappingVOTranslator predictionTranslator;

    @Mock
    private PerfLogHandler perfLogHandler;

    @InjectMocks
    private MappingAPIServiceImpl mappingAPIService;

    private ChannelProductsDTO testChannelProductsDTO;
    private MappingVO testMappingVO;
    private PredictionMappingVO testPredictionMappingVO;

    @BeforeEach
    void setUp() {
        testChannelProductsDTO = new ChannelProductsDTO();
        testChannelProductsDTO.setChannelId("TEST_CHANNEL");
        testChannelProductsDTO.setChannelHotelId("TEST_HOTEL");

        testMappingVO = new MappingVO();
        testMappingVO.setChannelId("TEST_CHANNEL");
        testMappingVO.setChannelHotelId("TEST_HOTEL");

        testPredictionMappingVO = new PredictionMappingVO();
        testPredictionMappingVO.setChannelId("TEST_CHANNEL");
        testPredictionMappingVO.setChannelHotelId("TEST_HOTEL");
    }

    @Test
    void testGetPredictMapping_WithValidParameters_ReturnsSuccessResult() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String hotelId = "HOTEL_123";
        PredictionModel predictionModel = PredictionModel.BERT;
        Boolean refresh = true;
        Double threshold = 0.8;
        Integer candidateCount = 10;
        Boolean enableEraseHeader = false;
        String echoToken = "TEST_ECHO_TOKEN";

        when(mappingPredictionService.getAIMapping(channelId, channelHotelId, hotelId, refresh, threshold))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);
            when(perfLogHandler.getToken()).thenReturn("ORIGINAL_TOKEN");

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    channelId, channelHotelId, hotelId, predictionModel, refresh, threshold, 
                    candidateCount, enableEraseHeader, echoToken);

            // Then
            assertNotNull(result, "Result should not be null");
            assertTrue(result.isSuccess(), "Result should be successful");
            assertEquals(testPredictionMappingVO, result.getData(), "Result data should match");
            
            verify(perfLogHandler).setToken(echoToken);
            verify(mappingPredictionService).getAIMapping(channelId, channelHotelId, hotelId, refresh, threshold);
            verify(predictionTranslator).map(testChannelProductsDTO);
        }
    }

    @Test
    void testGetPredictMapping_WithNullEchoToken_DoesNotSetToken() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String hotelId = "HOTEL_123";

        when(mappingPredictionService.getAIMapping(anyString(), anyString(), anyString(), anyBoolean(), anyDouble()))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    channelId, channelHotelId, hotelId, PredictionModel.BERT, true, 0.8, 
                    10, false, null);

            // Then
            assertNotNull(result, "Result should not be null");
            assertTrue(result.isSuccess(), "Result should be successful");
            
            verify(perfLogHandler, never()).setToken(anyString());
        }
    }

    @Test
    void testGetPredictMapping_WithEmptyEchoToken_DoesNotSetToken() {
        // Given
        when(mappingPredictionService.getAIMapping(anyString(), anyString(), anyString(), anyBoolean(), anyDouble()))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    "TEST_CHANNEL", "TEST_HOTEL", "HOTEL_123", PredictionModel.BERT, true, 0.8, 
                    10, false, "");

            // Then
            assertNotNull(result, "Result should not be null");
            assertTrue(result.isSuccess(), "Result should be successful");
            
            verify(perfLogHandler, never()).setToken(anyString());
        }
    }

    @Test
    void testGetPredictMapping_WithNullPerfLogHandler_HandlesGracefully() {
        // Given
        when(mappingPredictionService.getAIMapping(anyString(), anyString(), anyString(), anyBoolean(), anyDouble()))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(null);

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    "TEST_CHANNEL", "TEST_HOTEL", "HOTEL_123", PredictionModel.BERT, true, 0.8, 
                    10, false, "TEST_TOKEN");

            // Then
            assertNotNull(result, "Result should not be null");
            assertTrue(result.isSuccess(), "Result should be successful");
        }
    }

    @Test
    void testGetMapping_WithValidParameters_ReturnsSuccessResult() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        Boolean snapshot = true;

        when(mappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot))
                .thenReturn(testChannelProductsDTO);
        when(mappingVOTranslator.map(testChannelProductsDTO)).thenReturn(testMappingVO);

        // When
        UnifyResult<MappingVO> result = mappingAPIService.getMapping(channelId, channelHotelId, snapshot);

        // Then
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isSuccess(), "Result should be successful");
        assertEquals(testMappingVO, result.getData(), "Result data should match");
        
        verify(mappingService).getChannelProductsMapping(channelId, channelHotelId, snapshot);
        verify(mappingVOTranslator).map(testChannelProductsDTO);
    }

    @Test
    void testGetMapping_WithNullSnapshot_HandlesCorrectly() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";

        when(mappingService.getChannelProductsMapping(channelId, channelHotelId, null))
                .thenReturn(testChannelProductsDTO);
        when(mappingVOTranslator.map(testChannelProductsDTO)).thenReturn(testMappingVO);

        // When
        UnifyResult<MappingVO> result = mappingAPIService.getMapping(channelId, channelHotelId, null);

        // Then
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isSuccess(), "Result should be successful");
        
        verify(mappingService).getChannelProductsMapping(channelId, channelHotelId, null);
    }

    @Test
    void testUpdateMapping_WithValidParameters_ReturnsSuccessResult() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";

        when(mappingVOTranslator.reverseMap(testMappingVO)).thenReturn(testChannelProductsDTO);
        when(mappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO))
                .thenReturn(testChannelProductsDTO);
        when(mappingVOTranslator.map(testChannelProductsDTO)).thenReturn(testMappingVO);

        // When
        UnifyResult<MappingVO> result = mappingAPIService.updateMapping(channelId, channelHotelId, testMappingVO);

        // Then
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isSuccess(), "Result should be successful");
        assertEquals(testMappingVO, result.getData(), "Result data should match");
        
        verify(mappingVOTranslator).reverseMap(testMappingVO);
        verify(mappingService).setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO);
        verify(mappingVOTranslator).map(testChannelProductsDTO);
    }

    @Test
    void testGetPredictMapping_WithException_ReturnsErrorResult() {
        // Given
        when(mappingPredictionService.getAIMapping(anyString(), anyString(), anyString(), anyBoolean(), anyDouble()))
                .thenThrow(new RuntimeException("Service error"));

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    "TEST_CHANNEL", "TEST_HOTEL", "HOTEL_123", PredictionModel.BERT, true, 0.8, 
                    10, false, "TEST_TOKEN");

            // Then
            assertNotNull(result, "Result should not be null");
            assertFalse(result.isSuccess(), "Result should indicate failure");
        }
    }

    @Test
    void testGetMapping_WithException_ReturnsErrorResult() {
        // Given
        when(mappingService.getChannelProductsMapping(anyString(), anyString(), anyBoolean()))
                .thenThrow(new RuntimeException("Service error"));

        // When
        UnifyResult<MappingVO> result = mappingAPIService.getMapping("TEST_CHANNEL", "TEST_HOTEL", true);

        // Then
        assertNotNull(result, "Result should not be null");
        assertFalse(result.isSuccess(), "Result should indicate failure");
    }

    @Test
    void testUpdateMapping_WithException_ReturnsErrorResult() {
        // Given
        when(mappingVOTranslator.reverseMap(any(MappingVO.class)))
                .thenThrow(new RuntimeException("Translation error"));

        // When
        UnifyResult<MappingVO> result = mappingAPIService.updateMapping("TEST_CHANNEL", "TEST_HOTEL", testMappingVO);

        // Then
        assertNotNull(result, "Result should not be null");
        assertFalse(result.isSuccess(), "Result should indicate failure");
    }

    @Test
    void testGetPredictMapping_WithAllParameters_CallsServiceCorrectly() {
        // Given
        String channelId = "TEST_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        String hotelId = "HOTEL_123";
        PredictionModel predictionModel = PredictionModel.BERT;
        Boolean refresh = false;
        Double threshold = 0.9;
        Integer candidateCount = 5;
        Boolean enableEraseHeader = true;
        String echoToken = "ECHO_123";

        when(mappingPredictionService.getAIMapping(channelId, channelHotelId, hotelId, refresh, threshold))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);
            when(perfLogHandler.getToken()).thenReturn("ORIGINAL_TOKEN");

            // When
            mappingAPIService.getPredictMapping(channelId, channelHotelId, hotelId, predictionModel, 
                    refresh, threshold, candidateCount, enableEraseHeader, echoToken);

            // Then
            verify(mappingPredictionService).getAIMapping(channelId, channelHotelId, hotelId, refresh, threshold);
            verify(perfLogHandler).setToken(echoToken);
        }
    }

    @Test
    void testGetMapping_WithFalseSnapshot_CallsServiceCorrectly() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "HOTEL_456";
        Boolean snapshot = false;

        when(mappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot))
                .thenReturn(testChannelProductsDTO);
        when(mappingVOTranslator.map(testChannelProductsDTO)).thenReturn(testMappingVO);

        // When
        mappingAPIService.getMapping(channelId, channelHotelId, snapshot);

        // Then
        verify(mappingService).getChannelProductsMapping(channelId, channelHotelId, snapshot);
    }

    @Test
    void testUpdateMapping_WithNullMappingVO_HandlesCorrectly() {
        // Given
        when(mappingVOTranslator.reverseMap(null)).thenReturn(null);
        when(mappingService.setChannelProductsMapping(anyString(), anyString(), isNull()))
                .thenReturn(testChannelProductsDTO);
        when(mappingVOTranslator.map(testChannelProductsDTO)).thenReturn(testMappingVO);

        // When
        UnifyResult<MappingVO> result = mappingAPIService.updateMapping("TEST_CHANNEL", "TEST_HOTEL", null);

        // Then
        assertNotNull(result, "Result should not be null");
        verify(mappingVOTranslator).reverseMap(null);
    }

    @Test
    void testGetPredictMapping_WithNullThreshold_HandlesCorrectly() {
        // Given
        when(mappingPredictionService.getAIMapping(anyString(), anyString(), anyString(), anyBoolean(), isNull()))
                .thenReturn(testChannelProductsDTO);
        when(predictionTranslator.map(testChannelProductsDTO)).thenReturn(testPredictionMappingVO);

        try (MockedStatic<PerfLogHandler> mockedStatic = mockStatic(PerfLogHandler.class)) {
            mockedStatic.when(PerfLogHandler::currentHandler).thenReturn(perfLogHandler);

            // When
            UnifyResult<PredictionMappingVO> result = mappingAPIService.getPredictMapping(
                    "TEST_CHANNEL", "TEST_HOTEL", "HOTEL_123", PredictionModel.BERT, true, null, 
                    10, false, "TEST_TOKEN");

            // Then
            assertNotNull(result, "Result should not be null");
            assertTrue(result.isSuccess(), "Result should be successful");
            
            verify(mappingPredictionService).getAIMapping("TEST_CHANNEL", "TEST_HOTEL", "HOTEL_123", true, null);
        }
    }
}
