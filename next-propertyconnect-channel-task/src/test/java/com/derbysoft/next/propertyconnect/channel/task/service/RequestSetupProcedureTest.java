package com.derbysoft.next.propertyconnect.channel.task.service;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelHotelDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.channelremoteservice.RemoteChannelService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests for RequestSetupProcedure
 * Tests setup procedure logic and operation sequencing
 */
@ExtendWith(MockitoExtension.class)
class RequestSetupProcedureTest {

    @InjectMocks
    private RequestSetupProcedure requestSetupProcedure;

    private ChannelHotelDTO testChannelHotel;

    @BeforeEach
    void setUp() {
        testChannelHotel = new ChannelHotelDTO();
        testChannelHotel.setChannelId("TEST_CHANNEL");
        testChannelHotel.setChannelHotelId("TEST_HOTEL");
        testChannelHotel.setSupplierId("TEST_SUPPLIER");
    }

    @Test
    void testRequestSetupProcedure_ImplementsChannelHotelSetupProcedure() {
        // Given & When & Then
        assertTrue(requestSetupProcedure instanceof ChannelHotelSetupProcedure,
                "RequestSetupProcedure should implement ChannelHotelSetupProcedure");
    }

    @Test
    void testSetupProcedure_WithValidChannelHotel_ReturnsOperationsList() {
        // Given
        // When
        List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(testChannelHotel);

        // Then
        assertNotNull(operations, "Operations list should not be null");
        assertTrue(operations.size() > 0, "Should return at least one operation");
    }

    @Test
    void testSetupProcedure_WithNullChannelHotel_HandlesGracefully() {
        // Given
        ChannelHotelDTO nullChannelHotel = null;

        // When & Then
        assertDoesNotThrow(() -> {
            List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(nullChannelHotel);
            // Should handle null input gracefully
        });
    }

    @Test
    void testSetupProcedure_WithEmptyChannelHotel_ReturnsValidOperations() {
        // Given
        ChannelHotelDTO emptyChannelHotel = new ChannelHotelDTO();

        // When
        List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(emptyChannelHotel);

        // Then
        assertNotNull(operations, "Operations list should not be null even for empty DTO");
    }

    @Test
    void testSetupProcedure_ReturnsConsistentOperations() {
        // Given
        // When
        List<RemoteChannelService.Operation> operations1 = requestSetupProcedure.setupProcedure(testChannelHotel);
        List<RemoteChannelService.Operation> operations2 = requestSetupProcedure.setupProcedure(testChannelHotel);

        // Then
        assertEquals(operations1.size(), operations2.size(), "Should return same number of operations");
        for (int i = 0; i < operations1.size(); i++) {
            assertEquals(operations1.get(i), operations2.get(i), 
                    "Operations at index " + i + " should be the same");
        }
    }

    @Test
    void testSetupProcedure_ContainsExpectedOperations() {
        // Given
        // When
        List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(testChannelHotel);

        // Then
        assertNotNull(operations, "Operations list should not be null");
        // Check for common operations that should be present
        boolean hasValidOperations = operations.stream()
                .anyMatch(op -> op == RemoteChannelService.Operation.SaveCredential ||
                              op == RemoteChannelService.Operation.SaveProperty ||
                              op == RemoteChannelService.Operation.SaveRoomTypes ||
                              op == RemoteChannelService.Operation.SaveRatePlans ||
                              op == RemoteChannelService.Operation.SaveProducts ||
                              op == RemoteChannelService.Operation.TriggerARIRefresh);
        assertTrue(hasValidOperations, "Should contain at least one valid operation");
    }

    @Test
    void testDestination_ReturnsCorrectDestination() {
        // Given & When
        RemoteChannelService.Destination destination = requestSetupProcedure.destination();

        // Then
        assertNotNull(destination, "Destination should not be null");
        assertTrue(destination == RemoteChannelService.Destination.Adapter ||
                  destination == RemoteChannelService.Destination.Channel,
                  "Destination should be either Adapter or Channel");
    }

    @Test
    void testDestination_ReturnsConsistentValue() {
        // Given & When
        RemoteChannelService.Destination destination1 = requestSetupProcedure.destination();
        RemoteChannelService.Destination destination2 = requestSetupProcedure.destination();

        // Then
        assertEquals(destination1, destination2, "Destination should be consistent");
        assertSame(destination1, destination2, "Destination should return same enum instance");
    }

    @Test
    void testProcedure_IsAnnotatedWithSpringService() {
        // Given
        Class<?> procedureClass = RequestSetupProcedure.class;

        // When
        boolean hasServiceAnnotation = procedureClass.isAnnotationPresent(org.springframework.stereotype.Service.class);

        // Then
        assertTrue(hasServiceAnnotation, "Procedure should be annotated with @Service");
    }

    @Test
    void testProcedure_CanBeInstantiatedMultipleTimes() {
        // Given & When
        RequestSetupProcedure procedure1 = new RequestSetupProcedure();
        RequestSetupProcedure procedure2 = new RequestSetupProcedure();

        // Then
        assertNotNull(procedure1, "First instance should not be null");
        assertNotNull(procedure2, "Second instance should not be null");
        assertNotSame(procedure1, procedure2, "Instances should be different objects");
        
        assertEquals(procedure1.destination(), procedure2.destination(), "Both instances should return same destination");
    }

    @Test
    void testProcedure_ThreadSafety() {
        // Given
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        RemoteChannelService.Destination[] destinationResults = new RemoteChannelService.Destination[threadCount];
        List<RemoteChannelService.Operation>[] procedureResults = new List[threadCount];

        // When
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                destinationResults[index] = requestSetupProcedure.destination();
                procedureResults[index] = requestSetupProcedure.setupProcedure(testChannelHotel);
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Then
        RemoteChannelService.Destination expectedDestination = destinationResults[0];
        for (int i = 0; i < threadCount; i++) {
            assertEquals(expectedDestination, destinationResults[i], "All threads should return same destination");
            assertNotNull(procedureResults[i], "All threads should return valid procedure");
        }
    }

    @Test
    void testProcedure_SupportsPolymorphism() {
        // Given
        ChannelHotelSetupProcedure procedure = requestSetupProcedure;

        // When
        RemoteChannelService.Destination destination = procedure.destination();
        List<RemoteChannelService.Operation> operations = procedure.setupProcedure(testChannelHotel);

        // Then
        assertNotNull(destination, "Polymorphic call should work correctly");
        assertNotNull(operations, "Polymorphic call should return valid operations");
    }

    @Test
    void testProcedure_HandlesComplexChannelHotelData() {
        // Given
        ChannelHotelDTO complexChannelHotel = new ChannelHotelDTO();
        complexChannelHotel.setChannelId("COMPLEX_CHANNEL");
        complexChannelHotel.setChannelHotelId("COMPLEX_HOTEL");
        complexChannelHotel.setSupplierId("COMPLEX_SUPPLIER");

        // When
        List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(complexChannelHotel);

        // Then
        assertNotNull(operations, "Should handle complex data");
        assertTrue(operations.size() > 0, "Should return operations for complex data");
    }

    @Test
    void testProcedure_PerformanceCharacteristics() {
        // Given
        long startTime = System.nanoTime();

        // When
        for (int i = 0; i < 1000; i++) {
            requestSetupProcedure.setupProcedure(testChannelHotel);
            requestSetupProcedure.destination();
        }
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;

        // Then
        assertTrue(duration < 1_000_000_000, "1000 calls should complete within 1 second"); // 1 second in nanoseconds
    }

    @Test
    void testProcedure_MemoryEfficiency() {
        // Given & When
        List<RemoteChannelService.Operation> operations1 = requestSetupProcedure.setupProcedure(testChannelHotel);
        List<RemoteChannelService.Operation> operations2 = requestSetupProcedure.setupProcedure(testChannelHotel);

        // Then
        assertEquals(operations1, operations2, "Operations should be equal");
        // Operations may be different instances but should have same content
    }

    @Test
    void testProcedure_ToString() {
        // Given & When
        String toString = requestSetupProcedure.toString();

        // Then
        assertNotNull(toString, "toString should not return null");
        assertTrue(toString.contains("RequestSetupProcedure"), "toString should contain class name");
    }

    @Test
    void testProcedure_ClassMetadata() {
        // Given
        Class<?> procedureClass = RequestSetupProcedure.class;

        // When & Then
        assertEquals("RequestSetupProcedure", procedureClass.getSimpleName(), "Class name should be correct");
        assertTrue(procedureClass.getPackage().getName().contains("service"), "Package should contain service");
        assertEquals(1, procedureClass.getInterfaces().length, "Should implement exactly one interface");
        assertEquals(ChannelHotelSetupProcedure.class, procedureClass.getInterfaces()[0],
                "Should implement ChannelHotelSetupProcedure");
    }

    @Test
    void testProcedure_HandlesEdgeCases() {
        // Given
        ChannelHotelDTO edgeCaseHotel = new ChannelHotelDTO();
        edgeCaseHotel.setChannelId("");
        edgeCaseHotel.setChannelHotelId(null);
        edgeCaseHotel.setSupplierId("   ");

        // When & Then
        assertDoesNotThrow(() -> {
            List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(edgeCaseHotel);
            assertNotNull(operations, "Should handle edge cases gracefully");
        });
    }

    @Test
    void testProcedure_ValidatesOperationTypes() {
        // Given
        // When
        List<RemoteChannelService.Operation> operations = requestSetupProcedure.setupProcedure(testChannelHotel);

        // Then
        for (RemoteChannelService.Operation operation : operations) {
            assertNotNull(operation, "Each operation should not be null");
            assertTrue(operation instanceof RemoteChannelService.Operation, "Each operation should be valid enum value");
        }
    }
}
