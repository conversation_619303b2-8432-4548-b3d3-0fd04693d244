package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl;

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO;
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService;
import com.derbysoft.next.propertyconnect.channel.task.service.RemoteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.ObjectProvider;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for DefaultMappingServiceImpl
 * Tests default mapping service implementation with delegation pattern
 */
@ExtendWith(MockitoExtension.class)
class DefaultMappingServiceImplTest {

    @Mock
    private RemoteService remoteService;

    @Mock
    private ObjectProvider<MappingService> mappingServices;

    @Mock
    private MappingService specificMappingService;

    @Mock
    private MappingService anotherMappingService;

    @InjectMocks
    private DefaultMappingServiceImpl defaultMappingService;

    private ChannelProductsDTO testChannelProductsDTO;

    @BeforeEach
    void setUp() {
        testChannelProductsDTO = new ChannelProductsDTO();
        testChannelProductsDTO.setChannelId("TEST_CHANNEL");
        testChannelProductsDTO.setChannelHotelId("TEST_HOTEL");
    }

    @Test
    void testChannel_ReturnsAll() {
        // When
        String result = defaultMappingService.channel();

        // Then
        assertEquals("ALL", result, "Should return ALL as channel identifier");
    }

    @Test
    void testGetChannelProductsMapping_WithMatchingHandler_DelegatesToHandler() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";
        Boolean snapshot = true;

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot))
                .thenReturn(testChannelProductsDTO);
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(testChannelProductsDTO, result, "Should return result from specific handler");
        verify(specificMappingService).getChannelProductsMapping(channelId, channelHotelId, snapshot);
    }

    @Test
    void testGetChannelProductsMapping_WithNoMatchingHandler_ReturnsNull() {
        // Given
        String channelId = "UNKNOWN_CHANNEL";
        String channelHotelId = "TEST_HOTEL";
        Boolean snapshot = true;

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot);

        // Then
        assertNull(result, "Should return null when no matching handler found");
        verify(specificMappingService, never()).getChannelProductsMapping(anyString(), anyString(), anyBoolean());
    }

    @Test
    void testGetChannelProductsMapping_WithMultipleHandlers_ReturnsFirstMatch() {
        // Given
        String channelId = "EXPEDIA";
        String channelHotelId = "TEST_HOTEL";
        Boolean snapshot = false;

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(anotherMappingService.channel()).thenReturn("EXPEDIA");
        when(anotherMappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot))
                .thenReturn(testChannelProductsDTO);
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService, anotherMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, snapshot);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(testChannelProductsDTO, result, "Should return result from matching handler");
        verify(specificMappingService, never()).getChannelProductsMapping(anyString(), anyString(), anyBoolean());
        verify(anotherMappingService).getChannelProductsMapping(channelId, channelHotelId, snapshot);
    }

    @Test
    void testSetChannelProductsMapping_WithMatchingHandler_DelegatesToHandler() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO))
                .thenReturn(testChannelProductsDTO);
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(testChannelProductsDTO, result, "Should return result from specific handler");
        verify(specificMappingService).setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO);
    }

    @Test
    void testSetChannelProductsMapping_WithNoMatchingHandler_ReturnsNull() {
        // Given
        String channelId = "UNKNOWN_CHANNEL";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO);

        // Then
        assertNull(result, "Should return null when no matching handler found");
        verify(specificMappingService, never()).setChannelProductsMapping(anyString(), anyString(), any());
    }

    @Test
    void testGetChannelProductsMapping_WithEmptyHandlerStream_ReturnsNull() {
        // Given
        when(mappingServices.stream()).thenReturn(Stream.empty());

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping("TEST_CHANNEL", "TEST_HOTEL", true);

        // Then
        assertNull(result, "Should return null when no handlers available");
    }

    @Test
    void testSetChannelProductsMapping_WithEmptyHandlerStream_ReturnsNull() {
        // Given
        when(mappingServices.stream()).thenReturn(Stream.empty());

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping("TEST_CHANNEL", "TEST_HOTEL", testChannelProductsDTO);

        // Then
        assertNull(result, "Should return null when no handlers available");
    }

    @Test
    void testGetChannelProductsMapping_WithNullSnapshot_HandlesCorrectly() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.getChannelProductsMapping(channelId, channelHotelId, null))
                .thenReturn(testChannelProductsDTO);
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, null);

        // Then
        assertNotNull(result, "Result should not be null");
        verify(specificMappingService).getChannelProductsMapping(channelId, channelHotelId, null);
    }

    @Test
    void testSetChannelProductsMapping_WithNullChannelProductsDTO_HandlesCorrectly() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.setChannelProductsMapping(channelId, channelHotelId, null))
                .thenReturn(null);
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping(channelId, channelHotelId, null);

        // Then
        assertNull(result, "Result should be null");
        verify(specificMappingService).setChannelProductsMapping(channelId, channelHotelId, null);
    }

    @Test
    void testGetChannelProductsMapping_WithCaseInsensitiveChannelId_FindsExactMatch() {
        // Given
        String channelId = "booking"; // lowercase
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING"); // uppercase
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, true);

        // Then
        assertNull(result, "Should return null for case mismatch (exact match required)");
        verify(specificMappingService, never()).getChannelProductsMapping(anyString(), anyString(), anyBoolean());
    }

    @Test
    void testGetChannelProductsMapping_WithMultipleMatchingHandlers_ReturnsFirstMatch() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        MappingService firstBookingService = mock(MappingService.class);
        MappingService secondBookingService = mock(MappingService.class);

        when(firstBookingService.channel()).thenReturn("BOOKING");
        when(secondBookingService.channel()).thenReturn("BOOKING");
        when(firstBookingService.getChannelProductsMapping(channelId, channelHotelId, true))
                .thenReturn(testChannelProductsDTO);
        when(mappingServices.stream()).thenReturn(Stream.of(firstBookingService, secondBookingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, true);

        // Then
        assertNotNull(result, "Result should not be null");
        assertEquals(testChannelProductsDTO, result, "Should return result from first matching handler");
        verify(firstBookingService).getChannelProductsMapping(channelId, channelHotelId, true);
        verify(secondBookingService, never()).getChannelProductsMapping(anyString(), anyString(), anyBoolean());
    }

    @Test
    void testSetChannelProductsMapping_WithHandlerException_PropagatesException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO))
                .thenThrow(new RuntimeException("Handler error"));
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            defaultMappingService.setChannelProductsMapping(channelId, channelHotelId, testChannelProductsDTO);
        }, "Should propagate exception from handler");
    }

    @Test
    void testGetChannelProductsMapping_WithHandlerException_PropagatesException() {
        // Given
        String channelId = "BOOKING";
        String channelHotelId = "TEST_HOTEL";

        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(specificMappingService.getChannelProductsMapping(channelId, channelHotelId, true))
                .thenThrow(new RuntimeException("Handler error"));
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            defaultMappingService.getChannelProductsMapping(channelId, channelHotelId, true);
        }, "Should propagate exception from handler");
    }

    @Test
    void testGetHandler_WithNullChannelId_ReturnsEmpty() {
        // Given
        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping(null, "TEST_HOTEL", true);

        // Then
        assertNull(result, "Should return null for null channel ID");
    }

    @Test
    void testSetChannelProductsMapping_WithNullChannelId_ReturnsNull() {
        // Given
        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping(null, "TEST_HOTEL", testChannelProductsDTO);

        // Then
        assertNull(result, "Should return null for null channel ID");
    }

    @Test
    void testGetChannelProductsMapping_WithEmptyChannelId_ReturnsNull() {
        // Given
        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.getChannelProductsMapping("", "TEST_HOTEL", true);

        // Then
        assertNull(result, "Should return null for empty channel ID");
    }

    @Test
    void testSetChannelProductsMapping_WithEmptyChannelId_ReturnsNull() {
        // Given
        when(specificMappingService.channel()).thenReturn("BOOKING");
        when(mappingServices.stream()).thenReturn(Stream.of(specificMappingService));

        // When
        ChannelProductsDTO result = defaultMappingService.setChannelProductsMapping("", "TEST_HOTEL", testChannelProductsDTO);

        // Then
        assertNull(result, "Should return null for empty channel ID");
    }
}
