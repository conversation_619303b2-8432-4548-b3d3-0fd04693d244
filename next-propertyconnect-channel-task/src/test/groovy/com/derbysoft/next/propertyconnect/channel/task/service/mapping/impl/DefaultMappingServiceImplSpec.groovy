package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingService
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll

/**
 * Comprehensive Spock tests for DefaultMappingServiceImpl
 * Target: 80%+ line coverage for 72 lines of code
 * Tests basic mapping operations, null safety, field transformations
 * Validates mapping logic for different data types and edge cases
 */
class DefaultMappingServiceImplSpec extends Specification {

    @Shared
    DefaultMappingServiceImpl mappingService

    def setup() {
        mappingService = new DefaultMappingServiceImpl()
    }

    def cleanup() {
        // Clean up resources if needed
    }

    def "should implement MappingService interface"() {
        expect: "service implements the correct interface"
        mappingService instanceof MappingService
    }

    @Unroll
    def "should handle null input gracefully for method: #methodName"() {
        when: "calling method with null input"
        def result = mappingService."$methodName"(null)

        then: "should return null or empty result without throwing exception"
        result == null || result == [] || result == [:]

        where:
        methodName << ['mapProducts', 'mapChannelProducts', 'transformProductData']
    }

    def "should map empty product list correctly"() {
        given: "empty product list"
        def emptyProducts = []

        when: "mapping empty list"
        def result = mappingService.mapProducts(emptyProducts)

        then: "should return empty list"
        result != null
        result.isEmpty()
    }

    def "should map single product with all fields populated"() {
        given: "a complete product DTO"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            hotelId = "HOTEL_001"
            channelHotelId = "CH_HOTEL_001"
            roomId = "ROOM_001"
            channelRoomId = "CH_ROOM_001"
            roomName = "Deluxe Room"
            channelRoomName = "Channel Deluxe Room"
            rateId = "RATE_001"
            channelRateId = "CH_RATE_001"
            rateName = "Standard Rate"
            channelRateName = "Channel Standard Rate"
            status = "ACTIVE"
            mapped = true
            availStatus = true
            bedType = "KING"
            mealPlan = "BB"
            payType = "PREPAID"
            maxOccupancy = 2
            predictionScore = 0.95d
        }

        when: "mapping the product"
        def result = mappingService.mapProduct(product)

        then: "all fields should be mapped correctly"
        result != null
        result.supplierId == "SUPPLIER_001"
        result.channelId == "CHANNEL_001"
        result.hotelId == "HOTEL_001"
        result.roomName == "Deluxe Room"
        result.rateName == "Standard Rate"
        result.status == "ACTIVE"
        result.mapped == true
        result.availStatus == true
        result.maxOccupancy == 2
        result.predictionScore == 0.95d
    }

    @Unroll
    def "should handle product with missing fields: #scenario"() {
        given: "a product with missing fields"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = supplierId
            channelId = channelId
            roomName = roomName
            rateName = rateName
        }

        when: "mapping the product"
        def result = mappingService.mapProduct(product)

        then: "should handle missing fields gracefully"
        result != null
        result.supplierId == expectedSupplierId
        result.channelId == expectedChannelId

        where:
        scenario           | supplierId    | channelId     | roomName | rateName | expectedSupplierId | expectedChannelId
        "null supplier"    | null          | "CHANNEL_001" | "Room"   | "Rate"   | null               | "CHANNEL_001"
        "empty supplier"   | ""            | "CHANNEL_001" | "Room"   | "Rate"   | ""                 | "CHANNEL_001"
        "null channel"     | "SUPPLIER_001"| null          | "Room"   | "Rate"   | "SUPPLIER_001"     | null
        "empty channel"    | "SUPPLIER_001"| ""            | "Room"   | "Rate"   | "SUPPLIER_001"     | ""
    }

    def "should map product list with mixed valid and invalid products"() {
        given: "a list with valid and invalid products"
        def products = [
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_001"
                channelId = "CHANNEL_001"
                roomName = "Valid Room"
                rateName = "Valid Rate"
            },
            null, // Invalid product
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_002"
                channelId = "CHANNEL_002"
                roomName = "Another Valid Room"
                rateName = "Another Valid Rate"
            }
        ]

        when: "mapping the product list"
        def result = mappingService.mapProducts(products)

        then: "should filter out null products and map valid ones"
        result != null
        result.size() == 2
        result[0].supplierId == "SUPPLIER_001"
        result[1].supplierId == "SUPPLIER_002"
    }

    def "should handle concurrent mapping operations"() {
        given: "multiple products for concurrent processing"
        def products = (1..100).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_${i.toString().padLeft(3, '0')}"
                channelId = "CHANNEL_${i.toString().padLeft(3, '0')}"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
            }
        }

        when: "mapping products concurrently"
        def results = products.parallelStream()
            .map { product -> mappingService.mapProduct(product) }
            .collect()

        then: "all products should be mapped correctly"
        results.size() == 100
        results.every { it != null }
        results.every { it.supplierId.startsWith("SUPPLIER_") }
    }

    @Unroll
    def "should validate field constraints: #fieldName with value #fieldValue"() {
        given: "a product with specific field value"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            setProperty(fieldName, fieldValue)
        }

        when: "mapping the product"
        def result = mappingService.mapProduct(product)

        then: "should handle field validation correctly"
        result != null
        result.getProperty(fieldName) == expectedValue

        where:
        fieldName       | fieldValue | expectedValue
        "maxOccupancy"  | -1         | -1
        "maxOccupancy"  | 0          | 0
        "maxOccupancy"  | 10         | 10
        "predictionScore" | -0.5d    | -0.5d
        "predictionScore" | 0.0d     | 0.0d
        "predictionScore" | 1.5d     | 1.5d
        "mapped"        | true       | true
        "mapped"        | false      | false
        "availStatus"   | true       | true
        "availStatus"   | false      | false
    }

    def "should preserve extension data during mapping"() {
        given: "a product with extension data"
        def extensions = [
            "customField1": "value1",
            "customField2": 123,
            "customField3": true
        ]
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            extensions = extensions
        }

        when: "mapping the product"
        def result = mappingService.mapProduct(product)

        then: "extension data should be preserved"
        result != null
        result.extensions != null
        result.extensions.size() == 3
        result.extensions["customField1"] == "value1"
        result.extensions["customField2"] == 123
        result.extensions["customField3"] == true
    }

    def "should handle deep cloning of nested objects"() {
        given: "a product with nested candidate products"
        def candidateProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "CANDIDATE_SUPPLIER"
            channelId = "CANDIDATE_CHANNEL"
            roomName = "Candidate Room"
        }
        
        def mainProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "MAIN_SUPPLIER"
            channelId = "MAIN_CHANNEL"
            roomName = "Main Room"
            candidateProducts = [candidateProduct]
        }

        when: "mapping the product"
        def result = mappingService.mapProduct(mainProduct)

        then: "nested objects should be properly cloned"
        result != null
        result.candidateProducts != null
        result.candidateProducts.size() == 1
        result.candidateProducts[0].supplierId == "CANDIDATE_SUPPLIER"
        
        when: "modifying original"
        candidateProduct.supplierId = "MODIFIED_SUPPLIER"

        then: "mapped result should not be affected"
        result.candidateProducts[0].supplierId == "CANDIDATE_SUPPLIER"
    }

    def "should test mapping performance with large datasets"() {
        given: "large dataset of products"
        def largeProductList = (1..1000).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_${i.toString().padLeft(4, '0')}"
                channelId = "CHANNEL_${i % 10}"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                bedType = i % 2 == 0 ? "KING" : "QUEEN"
                maxOccupancy = 2
            }
        }

        when: "mapping large dataset"
        def startTime = System.currentTimeMillis()
        def results = mappingService.mapProducts(largeProductList)
        def processingTime = System.currentTimeMillis() - startTime

        then: "should process efficiently"
        results != null
        results.size() == 1000
        processingTime < 1000 // Should complete within 1 second
        results.every { it.supplierId.startsWith("SUPPLIER_") }
    }

    def "should handle mapping with custom transformation rules"() {
        given: "products requiring custom transformation"
        def products = [
            new ChannelProductsDTO.Product().tap {
                supplierId = "TRANSFORM_SUPPLIER"
                channelId = "TRANSFORM_CHANNEL"
                roomName = "DELUXE_SUITE_KING_BED"
                rateName = "BEST_AVAILABLE_RATE_PREPAID"
                status = "ACTIVE"
            }
        ]

        when: "applying custom transformation rules"
        def results = mappingService.mapProductsWithTransformation(products)

        then: "should apply transformations correctly"
        results != null
        results.size() == 1
        with(results[0]) {
            roomName == "Deluxe Suite King Bed" // Transformed from underscore format
            rateName == "Best Available Rate Prepaid" // Transformed from underscore format
            status == "ACTIVE"
        }
    }

    def "should validate mapping consistency across multiple calls"() {
        given: "identical product for consistency test"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "CONSISTENCY_SUPPLIER"
            channelId = "CONSISTENCY_CHANNEL"
            roomName = "Consistency Room"
            rateName = "Consistency Rate"
            predictionScore = 0.85
        }

        when: "mapping same product multiple times"
        def results = (1..10).collect {
            mappingService.mapProduct(product)
        }

        then: "should return consistent results"
        results.every { it != null }
        results.every { it.supplierId == "CONSISTENCY_SUPPLIER" }
        results.every { it.predictionScore == 0.85 }

        and: "all results should be identical"
        def firstResult = results[0]
        results.every { result ->
            result.supplierId == firstResult.supplierId &&
            result.channelId == firstResult.channelId &&
            result.roomName == firstResult.roomName &&
            result.rateName == firstResult.rateName &&
            result.predictionScore == firstResult.predictionScore
        }
    }

    def "should handle mapping with validation errors"() {
        given: "products with validation issues"
        def invalidProducts = [
            new ChannelProductsDTO.Product().tap {
                supplierId = "INVALID_SUPPLIER"
                channelId = "INVALID_CHANNEL"
                roomName = "A" * 1000 // Extremely long name
                rateName = "B" * 1000 // Extremely long name
                maxOccupancy = -5 // Invalid occupancy
            }
        ]

        when: "mapping products with validation errors"
        def results = mappingService.mapProductsWithValidation(invalidProducts)

        then: "should handle validation errors gracefully"
        results != null
        results.size() == 1
        with(results[0]) {
            supplierId == "INVALID_SUPPLIER"
            channelId == "INVALID_CHANNEL"
            roomName.length() <= 255 // Should be truncated
            rateName.length() <= 255 // Should be truncated
            maxOccupancy == 0 // Should be corrected to valid value
        }
    }

    def "should test mapping with different data types"() {
        given: "product with various data types"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "DATATYPE_SUPPLIER"
            channelId = "DATATYPE_CHANNEL"
            roomName = "Data Type Room"
            rateName = "Data Type Rate"
            maxOccupancy = 2
            predictionScore = 0.75
            mapped = true
            availStatus = false
            extensions = [
                stringValue: "test string",
                intValue: 42,
                doubleValue: 3.14159,
                booleanValue: true,
                listValue: [1, 2, 3],
                mapValue: [nested: "value"]
            ]
        }

        when: "mapping product with various data types"
        def result = mappingService.mapProduct(product)

        then: "should preserve all data types correctly"
        result != null
        result.maxOccupancy instanceof Integer
        result.predictionScore instanceof Double
        result.mapped instanceof Boolean
        result.availStatus instanceof Boolean
        result.extensions["stringValue"] instanceof String
        result.extensions["intValue"] instanceof Integer
        result.extensions["doubleValue"] instanceof Double
        result.extensions["booleanValue"] instanceof Boolean
        result.extensions["listValue"] instanceof List
        result.extensions["mapValue"] instanceof Map
    }

    def "should handle mapping with circular references"() {
        given: "products with potential circular references"
        def product1 = new ChannelProductsDTO.Product().tap {
            supplierId = "CIRCULAR_SUPPLIER"
            channelId = "CIRCULAR_CHANNEL"
            roomName = "Circular Room 1"
            rateName = "Circular Rate 1"
        }

        def product2 = new ChannelProductsDTO.Product().tap {
            supplierId = "CIRCULAR_SUPPLIER"
            channelId = "CIRCULAR_CHANNEL"
            roomName = "Circular Room 2"
            rateName = "Circular Rate 2"
        }

        // Create circular reference
        product1.candidateProducts = [product2]
        product2.candidateProducts = [product1]

        when: "mapping products with circular references"
        def result1 = mappingService.mapProduct(product1)
        def result2 = mappingService.mapProduct(product2)

        then: "should handle circular references without infinite loops"
        result1 != null
        result2 != null
        result1.roomName == "Circular Room 1"
        result2.roomName == "Circular Room 2"
        // Should not cause stack overflow or infinite recursion
    }
}
