package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl

import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.MappingPredictionClient
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionReportService
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report.ReportData
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl.report.ReportMetrics
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll
import spock.lang.Timeout

import java.util.concurrent.TimeUnit
import java.time.LocalDateTime

/**
 * Comprehensive Spock tests for PredictionReportServiceImpl
 * Target: 80%+ line coverage for 1,092 lines of code
 * Tests report generation, data aggregation, formatting, and error handling
 */
class PredictionReportServiceImplSpec extends Specification {

    @Shared
    PredictionReportServiceImpl reportService

    MappingPredictionClient mockPredictionClient = Mock()

    def setup() {
        reportService = new PredictionReportServiceImpl()
        reportService.mappingPredictionClient = mockPredictionClient
    }

    def "should implement PredictionReportService interface"() {
        expect: "service implements the correct interface"
        reportService instanceof PredictionReportService
    }

    def "should generate basic prediction report successfully"() {
        given: "sample channel products data"
        def channelProducts = createSampleChannelProducts(5)

        and: "mock prediction client returns predictions"
        mockPredictionClient.predictMapping(_) >> createMockPredictions(5)

        when: "generating prediction report"
        def report = reportService.generatePredictionReport(channelProducts)

        then: "report should be generated successfully"
        report != null
        report.totalProducts == 5
        report.reportGeneratedAt != null
        report.metrics != null
        report.summary != null
        1 * mockPredictionClient.predictMapping(_)
    }

    def "should handle null input gracefully"() {
        when: "generating report with null input"
        def report = reportService.generatePredictionReport(null)

        then: "should return empty report without calling client"
        report != null
        report.totalProducts == 0
        report.errorMessage == "No input data provided"
        0 * mockPredictionClient._
    }

    def "should handle empty product list"() {
        given: "empty product list"
        def emptyProducts = new ChannelProductsDTO().tap {
            channelProducts = []
        }

        when: "generating report"
        def report = reportService.generatePredictionReport(emptyProducts)

        then: "should return empty report"
        report != null
        report.totalProducts == 0
        report.summary.contains("No products to analyze")
        0 * mockPredictionClient._
    }

    @Unroll
    def "should calculate prediction metrics correctly for #scenario"() {
        given: "products with different prediction scores"
        def products = createProductsWithScores(scores)

        and: "mock predictions"
        mockPredictionClient.predictMapping(_) >> createPredictionsWithScores(scores)

        when: "generating report"
        def report = reportService.generatePredictionReport(products)

        then: "metrics should be calculated correctly"
        report.metrics.averageScore == expectedAverage
        report.metrics.highConfidenceCount == expectedHighCount
        report.metrics.lowConfidenceCount == expectedLowCount
        report.metrics.totalProcessed == scores.size()

        where:
        scenario              | scores                    | expectedAverage | expectedHighCount | expectedLowCount
        "all high scores"     | [0.95, 0.90, 0.85, 0.80] | 0.875          | 4                | 0
        "all low scores"      | [0.30, 0.25, 0.20, 0.15] | 0.225          | 0                | 4
        "mixed scores"        | [0.95, 0.60, 0.30, 0.85] | 0.675          | 2                | 2
        "single high score"   | [0.90]                    | 0.90           | 1                | 0
        "single low score"    | [0.40]                    | 0.40           | 0                | 1
    }

    def "should generate detailed report sections"() {
        given: "complex product data"
        def products = createComplexChannelProducts()

        and: "mock predictions with various confidence levels"
        mockPredictionClient.predictMapping(_) >> createComplexPredictions()

        when: "generating detailed report"
        def report = reportService.generateDetailedReport(products)

        then: "report should contain all sections"
        report.executiveSummary != null
        report.detailedAnalysis != null
        report.recommendationsSection != null
        report.appendixData != null
        report.charts != null
        report.tables != null
        
        and: "sections should have content"
        !report.executiveSummary.isEmpty()
        !report.detailedAnalysis.isEmpty()
        report.recommendationsSection.size() > 0
    }

    def "should handle prediction client timeout gracefully"() {
        given: "products for prediction"
        def products = createSampleChannelProducts(3)

        and: "mock client throws timeout exception"
        mockPredictionClient.predictMapping(_) >> {
            throw new RuntimeException("Connection timeout")
        }

        when: "generating report"
        def report = reportService.generatePredictionReport(products)

        then: "should handle timeout and return error report"
        report != null
        report.hasErrors == true
        report.errorMessage.contains("timeout")
        report.totalProducts == 3
        report.metrics.totalProcessed == 0
    }

    def "should aggregate data by channel correctly"() {
        given: "products from multiple channels"
        def products = createMultiChannelProducts()

        and: "mock predictions"
        mockPredictionClient.predictMapping(_) >> createMultiChannelPredictions()

        when: "generating channel aggregation report"
        def report = reportService.generateChannelAggregationReport(products)

        then: "should aggregate by channel"
        report.channelBreakdown.size() == 3
        report.channelBreakdown.containsKey("BOOKINGCOM")
        report.channelBreakdown.containsKey("AGODA")
        report.channelBreakdown.containsKey("EXPEDIA")
        
        and: "each channel should have metrics"
        report.channelBreakdown.each { channel, metrics ->
            assert metrics.totalProducts > 0
            assert metrics.averageScore >= 0.0
            assert metrics.averageScore <= 1.0
        }
    }

    def "should generate time-series analysis"() {
        given: "historical prediction data"
        def historicalData = createHistoricalPredictionData()

        when: "generating time-series report"
        def report = reportService.generateTimeSeriesReport(historicalData)

        then: "should contain time-series analysis"
        report.timeSeriesData != null
        report.trendAnalysis != null
        report.seasonalPatterns != null
        report.forecastData != null
        
        and: "trend analysis should be meaningful"
        report.trendAnalysis.overallTrend in ["IMPROVING", "DECLINING", "STABLE"]
        report.trendAnalysis.confidenceLevel > 0.0
    }

    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    def "should handle large dataset efficiently"() {
        given: "large dataset"
        def largeProducts = createSampleChannelProducts(1000)

        and: "mock predictions for large dataset"
        mockPredictionClient.predictMapping(_) >> createMockPredictions(1000)

        when: "generating report for large dataset"
        def startTime = System.currentTimeMillis()
        def report = reportService.generatePredictionReport(largeProducts)
        def processingTime = System.currentTimeMillis() - startTime

        then: "should process efficiently"
        report != null
        report.totalProducts == 1000
        processingTime < 5000 // Should complete within 5 seconds
        report.metrics.totalProcessed == 1000
    }

    def "should generate confidence distribution analysis"() {
        given: "products with varied confidence scores"
        def products = createProductsWithVariedConfidence()

        and: "mock predictions"
        mockPredictionClient.predictMapping(_) >> createVariedConfidencePredictions()

        when: "generating confidence distribution report"
        def report = reportService.generateConfidenceDistributionReport(products)

        then: "should analyze confidence distribution"
        report.distributionBuckets.size() == 10 // 0.0-0.1, 0.1-0.2, etc.
        report.distributionBuckets.each { bucket ->
            assert bucket.range != null
            assert bucket.count >= 0
            assert bucket.percentage >= 0.0
        }
        
        and: "should calculate statistics"
        report.statistics.mean > 0.0
        report.statistics.median > 0.0
        report.statistics.standardDeviation >= 0.0
    }

    def "should handle concurrent report generation"() {
        given: "multiple datasets for concurrent processing"
        def datasets = (1..5).collect { createSampleChannelProducts(10) }

        and: "mock predictions for each dataset"
        mockPredictionClient.predictMapping(_) >> { args ->
            createMockPredictions(10)
        }

        when: "generating reports concurrently"
        def reports = datasets.parallelStream()
            .map { dataset -> reportService.generatePredictionReport(dataset) }
            .collect()

        then: "all reports should be generated successfully"
        reports.size() == 5
        reports.every { it != null && it.totalProducts == 10 }
        (5.._) * mockPredictionClient.predictMapping(_)
    }

    def "should validate report data integrity"() {
        given: "products with specific characteristics"
        def products = createProductsWithKnownCharacteristics()

        and: "mock predictions with known results"
        mockPredictionClient.predictMapping(_) >> createKnownPredictionResults()

        when: "generating report"
        def report = reportService.generatePredictionReport(products)

        then: "report data should be consistent"
        report.totalProducts == products.channelProducts.size()
        report.metrics.totalProcessed <= report.totalProducts
        report.metrics.highConfidenceCount + report.metrics.lowConfidenceCount <= report.metrics.totalProcessed
        report.metrics.averageScore >= 0.0 && report.metrics.averageScore <= 1.0
        
        and: "percentages should add up correctly"
        def totalPercentage = report.metrics.highConfidencePercentage + report.metrics.lowConfidencePercentage
        Math.abs(totalPercentage - 100.0) < 0.01 || report.metrics.totalProcessed == 0
    }

    // Helper methods for test data creation
    private ChannelProductsDTO createSampleChannelProducts(int count) {
        new ChannelProductsDTO().tap {
            supplierId = "TEST_SUPPLIER"
            hotelId = "TEST_HOTEL"
            channelId = "TEST_CHANNEL"
            channelProducts = (1..count).collect { i ->
                new ChannelProductsDTO.Product().tap {
                    supplierId = "TEST_SUPPLIER"
                    channelId = "TEST_CHANNEL"
                    roomName = "Room ${i}"
                    rateName = "Rate ${i}"
                    status = "ACTIVE"
                }
            }
        }
    }

    private List<ChannelProductsDTO.Product> createMockPredictions(int count) {
        (1..count).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "TEST_SUPPLIER"
                channelId = "TEST_CHANNEL"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                predictionScore = 0.75 + (i % 3) * 0.1 // Varied scores
                mapped = predictionScore > 0.7
            }
        }
    }

    private ChannelProductsDTO createProductsWithScores(List<Double> scores) {
        new ChannelProductsDTO().tap {
            supplierId = "TEST_SUPPLIER"
            hotelId = "TEST_HOTEL"
            channelId = "TEST_CHANNEL"
            channelProducts = scores.withIndex().collect { score, i ->
                new ChannelProductsDTO.Product().tap {
                    supplierId = "TEST_SUPPLIER"
                    channelId = "TEST_CHANNEL"
                    roomName = "Room ${i}"
                    rateName = "Rate ${i}"
                    status = "ACTIVE"
                }
            }
        }
    }

    private List<ChannelProductsDTO.Product> createPredictionsWithScores(List<Double> scores) {
        scores.withIndex().collect { score, i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "TEST_SUPPLIER"
                channelId = "TEST_CHANNEL"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                predictionScore = score
                mapped = score > 0.7
            }
        }
    }

    private ChannelProductsDTO createComplexChannelProducts() {
        new ChannelProductsDTO().tap {
            supplierId = "COMPLEX_SUPPLIER"
            hotelId = "COMPLEX_HOTEL"
            channelId = "COMPLEX_CHANNEL"
            channelProducts = [
                new ChannelProductsDTO.Product().tap {
                    roomName = "Deluxe Suite"
                    rateName = "Best Available Rate"
                    bedType = "KING"
                    mealPlan = "BB"
                    maxOccupancy = 2
                },
                new ChannelProductsDTO.Product().tap {
                    roomName = "Standard Room"
                    rateName = "Advance Purchase"
                    bedType = "QUEEN"
                    mealPlan = "RO"
                    maxOccupancy = 2
                }
            ]
        }
    }

    private List<ChannelProductsDTO.Product> createComplexPredictions() {
        [
            new ChannelProductsDTO.Product().tap {
                roomName = "Deluxe Suite"
                rateName = "Best Available Rate"
                predictionScore = 0.92
                mapped = true
            },
            new ChannelProductsDTO.Product().tap {
                roomName = "Standard Room"
                rateName = "Advance Purchase"
                predictionScore = 0.45
                mapped = false
            }
        ]
    }

    private ChannelProductsDTO createMultiChannelProducts() {
        new ChannelProductsDTO().tap {
            supplierId = "MULTI_SUPPLIER"
            hotelId = "MULTI_HOTEL"
            channelProducts = [
                new ChannelProductsDTO.Product().tap {
                    channelId = "BOOKINGCOM"
                    roomName = "Booking Room 1"
                    rateName = "Booking Rate 1"
                },
                new ChannelProductsDTO.Product().tap {
                    channelId = "AGODA"
                    roomName = "Agoda Room 1"
                    rateName = "Agoda Rate 1"
                },
                new ChannelProductsDTO.Product().tap {
                    channelId = "EXPEDIA"
                    roomName = "Expedia Room 1"
                    rateName = "Expedia Rate 1"
                }
            ]
        }
    }

    private List<ChannelProductsDTO.Product> createMultiChannelPredictions() {
        [
            new ChannelProductsDTO.Product().tap {
                channelId = "BOOKINGCOM"
                predictionScore = 0.85
                mapped = true
            },
            new ChannelProductsDTO.Product().tap {
                channelId = "AGODA"
                predictionScore = 0.72
                mapped = true
            },
            new ChannelProductsDTO.Product().tap {
                channelId = "EXPEDIA"
                predictionScore = 0.58
                mapped = false
            }
        ]
    }

    private Map<String, Object> createHistoricalPredictionData() {
        [
            "timeRange": "2023-01-01 to 2023-12-31",
            "dataPoints": [
                [date: "2023-01-01", averageScore: 0.65, totalPredictions: 100],
                [date: "2023-06-01", averageScore: 0.72, totalPredictions: 150],
                [date: "2023-12-01", averageScore: 0.78, totalPredictions: 200]
            ],
            "channels": ["BOOKINGCOM", "AGODA", "EXPEDIA"]
        ]
    }

    private ChannelProductsDTO createProductsWithVariedConfidence() {
        new ChannelProductsDTO().tap {
            supplierId = "VARIED_SUPPLIER"
            hotelId = "VARIED_HOTEL"
            channelId = "VARIED_CHANNEL"
            channelProducts = (1..50).collect { i ->
                new ChannelProductsDTO.Product().tap {
                    roomName = "Room ${i}"
                    rateName = "Rate ${i}"
                    status = "ACTIVE"
                }
            }
        }
    }

    private List<ChannelProductsDTO.Product> createVariedConfidencePredictions() {
        (1..50).collect { i ->
            new ChannelProductsDTO.Product().tap {
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                predictionScore = Math.random()
                mapped = predictionScore > 0.7
            }
        }
    }

    private ChannelProductsDTO createProductsWithKnownCharacteristics() {
        new ChannelProductsDTO().tap {
            supplierId = "KNOWN_SUPPLIER"
            hotelId = "KNOWN_HOTEL"
            channelId = "KNOWN_CHANNEL"
            channelProducts = [
                new ChannelProductsDTO.Product().tap {
                    roomName = "Known Room 1"
                    rateName = "Known Rate 1"
                    status = "ACTIVE"
                },
                new ChannelProductsDTO.Product().tap {
                    roomName = "Known Room 2"
                    rateName = "Known Rate 2"
                    status = "ACTIVE"
                }
            ]
        }
    }

    private List<ChannelProductsDTO.Product> createKnownPredictionResults() {
        [
            new ChannelProductsDTO.Product().tap {
                roomName = "Known Room 1"
                rateName = "Known Rate 1"
                predictionScore = 0.90
                mapped = true
            },
            new ChannelProductsDTO.Product().tap {
                roomName = "Known Room 2"
                rateName = "Known Rate 2"
                predictionScore = 0.30
                mapped = false
            }
        ]
    }
}
