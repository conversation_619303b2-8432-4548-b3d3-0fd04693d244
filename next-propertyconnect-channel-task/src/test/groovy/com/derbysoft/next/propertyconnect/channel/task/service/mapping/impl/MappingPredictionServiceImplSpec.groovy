package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl

import com.derbysoft.next.propertyconnect.channel.task.client.aimapping.MappingPredictionClient
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.MappingPredictionService
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll

/**
 * Phase 1 - Mapping & Utility Tests
 * Comprehensive Spock tests for MappingPredictionServiceImpl
 * Tests AI mapping prediction logic, HTTP client mocking, error handling
 */
class MappingPredictionServiceImplSpec extends Specification {

    @Shared
    MappingPredictionServiceImpl predictionService

    MappingPredictionClient mockMappingClient = Mock()

    def setup() {
        predictionService = new MappingPredictionServiceImpl()
        predictionService.mappingPredictionClient = mockMappingClient
    }

    def cleanup() {
        // Reset mocks if needed
    }

    def "should implement MappingPredictionService interface"() {
        expect: "service implements the correct interface"
        predictionService instanceof MappingPredictionService
    }

    def "should handle null input gracefully"() {
        when: "calling prediction with null input"
        def result = predictionService.predictMapping(null)

        then: "should return null without calling client"
        result == null
        0 * mockMappingClient._
    }

    def "should handle empty product list"() {
        given: "empty product list"
        def emptyProducts = []

        when: "predicting mapping for empty list"
        def result = predictionService.predictMapping(emptyProducts)

        then: "should return empty list without calling client"
        result != null
        result.isEmpty()
        0 * mockMappingClient._
    }

    def "should successfully predict mapping for single product"() {
        given: "a product for prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Deluxe Room"
            rateName = "Standard Rate"
            bedType = "KING"
            mealPlan = "BB"
        }

        and: "mock client returns prediction result"
        def expectedPrediction = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Deluxe Room"
            rateName = "Standard Rate"
            predictionScore = 0.85d
            mapped = true
        }

        when: "predicting mapping"
        def result = predictionService.predictMapping([product])

        then: "should call client and return prediction"
        1 * mockMappingClient.predictMapping(_) >> [expectedPrediction]
        result != null
        result.size() == 1
        result[0].predictionScore == 0.85d
        result[0].mapped == true
    }

    @Unroll
    def "should handle different prediction scores: #score"() {
        given: "a product for prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Test Room"
            rateName = "Test Rate"
        }

        and: "mock client returns prediction with specific score"
        def prediction = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            predictionScore = score
            mapped = score >= 0.7d
        }

        when: "predicting mapping"
        def result = predictionService.predictMapping([product])

        then: "should return prediction with correct mapping status"
        1 * mockMappingClient.predictMapping(_) >> [prediction]
        result != null
        result.size() == 1
        result[0].predictionScore == score
        result[0].mapped == expectedMapped

        where:
        score | expectedMapped
        0.95d | true
        0.85d | true
        0.75d | true
        0.70d | true
        0.65d | false
        0.50d | false
        0.25d | false
        0.05d | false
    }

    def "should handle client timeout exception"() {
        given: "a product for prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Test Room"
            rateName = "Test Rate"
        }

        and: "mock client throws timeout exception"
        mockMappingClient.predictMapping(_) >> { throw new RuntimeException("Connection timeout") }

        when: "predicting mapping"
        def result = predictionService.predictMapping([product])

        then: "should handle exception gracefully"
        1 * mockMappingClient.predictMapping(_)
        result != null
        result.isEmpty() || result[0].predictionScore == 0.0d
    }

    def "should handle client service unavailable exception"() {
        given: "a product for prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Test Room"
            rateName = "Test Rate"
        }

        and: "mock client throws service unavailable exception"
        mockMappingClient.predictMapping(_) >> { throw new RuntimeException("Service unavailable") }

        when: "predicting mapping"
        def result = predictionService.predictMapping([product])

        then: "should handle exception and return fallback result"
        1 * mockMappingClient.predictMapping(_)
        result != null
        // Should return original products with default prediction scores
        result.size() == 1
        result[0].supplierId == "SUPPLIER_001"
        result[0].predictionScore == 0.0d || result[0].predictionScore == null
    }

    def "should handle batch prediction for multiple products"() {
        given: "multiple products for prediction"
        def products = (1..5).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_${i.toString().padLeft(3, '0')}"
                channelId = "CHANNEL_${i.toString().padLeft(3, '0')}"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
            }
        }

        and: "mock client returns predictions for all products"
        def predictions = products.collect { product ->
            new ChannelProductsDTO.Product().tap {
                supplierId = product.supplierId
                channelId = product.channelId
                roomName = product.roomName
                rateName = product.rateName
                predictionScore = 0.80d
                mapped = true
            }
        }

        when: "predicting mapping for batch"
        def result = predictionService.predictMapping(products)

        then: "should call client once and return all predictions"
        1 * mockMappingClient.predictMapping(_) >> predictions
        result != null
        result.size() == 5
        result.every { it.predictionScore == 0.80d }
        result.every { it.mapped == true }
    }

    def "should handle partial prediction failure"() {
        given: "multiple products for prediction"
        def products = [
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_001"
                channelId = "CHANNEL_001"
                roomName = "Valid Room"
                rateName = "Valid Rate"
            },
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_002"
                channelId = "CHANNEL_002"
                roomName = "Another Room"
                rateName = "Another Rate"
            }
        ]

        and: "mock client returns partial results"
        def partialPredictions = [
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_001"
                channelId = "CHANNEL_001"
                predictionScore = 0.90d
                mapped = true
            }
            // Missing prediction for second product
        ]

        when: "predicting mapping"
        def result = predictionService.predictMapping(products)

        then: "should handle partial results gracefully"
        1 * mockMappingClient.predictMapping(_) >> partialPredictions
        result != null
        result.size() >= 1
        result[0].predictionScore == 0.90d
    }

    def "should preserve original product data in predictions"() {
        given: "a product with extension data"
        def extensions = ["customField": "customValue"]
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Test Room"
            rateName = "Test Rate"
            bedType = "KING"
            mealPlan = "BB"
            maxOccupancy = 2
            extensions = extensions
        }

        and: "mock client returns prediction"
        def prediction = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Test Room"
            rateName = "Test Rate"
            predictionScore = 0.88d
            mapped = true
            extensions = extensions
        }

        when: "predicting mapping"
        def result = predictionService.predictMapping([product])

        then: "should preserve all original data"
        1 * mockMappingClient.predictMapping(_) >> [prediction]
        result != null
        result.size() == 1
        with(result[0]) {
            supplierId == "SUPPLIER_001"
            channelId == "CHANNEL_001"
            roomName == "Test Room"
            rateName == "Test Rate"
            predictionScore == 0.88d
            mapped == true
            extensions["customField"] == "customValue"
        }
    }

    def "should handle concurrent prediction requests"() {
        given: "multiple products for concurrent processing"
        def products = (1..10).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_${i.toString().padLeft(2, '0')}"
                channelId = "CHANNEL_${i.toString().padLeft(2, '0')}"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
            }
        }

        and: "mock client returns predictions"
        mockMappingClient.predictMapping(_) >> { args ->
            def inputProducts = args[0] as List<ChannelProductsDTO.Product>
            return inputProducts.collect { product ->
                new ChannelProductsDTO.Product().tap {
                    supplierId = product.supplierId
                    channelId = product.channelId
                    predictionScore = 0.75d
                    mapped = true
                }
            }
        }

        when: "making concurrent prediction requests"
        def futures = (1..5).collect {
            Thread.start {
                predictionService.predictMapping(products.take(2))
            }
        }
        futures.each { it.join() }

        then: "should handle concurrent requests without issues"
        (5.._) * mockMappingClient.predictMapping(_)
    }
}
