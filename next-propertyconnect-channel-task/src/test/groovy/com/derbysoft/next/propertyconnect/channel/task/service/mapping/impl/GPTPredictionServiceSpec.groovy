package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl

import com.derbysoft.next.propertyconnect.channel.task.client.openai.OpenAIClient
import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.GPTPredictionService
import feign.FeignException
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll
import spock.lang.Timeout

import java.util.concurrent.TimeUnit

/**
 * Comprehensive Spock tests for GPTPredictionService
 * Target: 80%+ line coverage for GPT integration
 * Tests GPT integration, prompt engineering, response parsing, rate limiting scenarios
 */
class GPTPredictionServiceSpec extends Specification {

    @Shared
    GPTPredictionService gptPredictionService

    OpenAIClient mockOpenAIClient = Mock()

    def setup() {
        gptPredictionService = new GPTPredictionService()
        gptPredictionService.openAIClient = mockOpenAIClient
        gptPredictionService.apiKey = "test-api-key"
        gptPredictionService.model = "gpt-3.5-turbo"
        gptPredictionService.maxTokens = 1000
        gptPredictionService.temperature = 0.3
    }

    def "should implement GPTPredictionService interface"() {
        expect: "service implements the correct interface"
        gptPredictionService instanceof GPTPredictionService
    }

    def "should initialize with default configuration"() {
        when: "creating new service instance"
        def service = new GPTPredictionService()

        then: "should have default settings"
        service != null
        service.getModel() == "gpt-3.5-turbo"
        service.getMaxTokens() == 1000
        service.getTemperature() == 0.3
        service.getMaxRetries() == 3
    }

    def "should generate prediction prompt correctly"() {
        given: "a product for prompt generation"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Deluxe Ocean View Suite"
            rateName = "Best Available Rate"
            bedType = "KING"
            mealPlan = "BB"
            maxOccupancy = 2
        }

        when: "generating prediction prompt"
        def prompt = gptPredictionService.generatePredictionPrompt(product)

        then: "should create comprehensive prompt"
        prompt != null
        prompt.contains("Deluxe Ocean View Suite")
        prompt.contains("Best Available Rate")
        prompt.contains("KING")
        prompt.contains("BB")
        prompt.contains("mapping prediction")
        prompt.length() > 100
    }

    def "should predict single product using GPT successfully"() {
        given: "a product for GPT prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Executive Suite"
            rateName = "Corporate Rate"
            bedType = "KING"
            mealPlan = "BB"
        }

        and: "mock OpenAI client returns prediction response"
        mockOpenAIClient.createCompletion(_) >> createGPTResponse(0.85, "High confidence mapping")

        when: "predicting with GPT"
        def result = gptPredictionService.predictSingle(product)

        then: "should return GPT prediction"
        result != null
        result.predictionScore == 0.85
        result.mapped == true
        result.gptExplanation == "High confidence mapping"
        result.supplierId == product.supplierId
        result.channelId == product.channelId
        1 * mockOpenAIClient.createCompletion(_)
    }

    def "should handle null product gracefully"() {
        when: "predicting null product"
        def result = gptPredictionService.predictSingle(null)

        then: "should return null without calling GPT"
        result == null
        0 * mockOpenAIClient.createCompletion(_)
    }

    @Unroll
    def "should parse GPT response with different confidence levels: #scenario"() {
        given: "a standard product"
        def product = createStandardProduct()

        and: "mock GPT response with specific confidence"
        mockOpenAIClient.createCompletion(_) >> createGPTResponse(score, explanation)

        when: "predicting with GPT"
        def result = gptPredictionService.predictSingle(product)

        then: "should parse confidence correctly"
        result != null
        result.predictionScore == score
        result.mapped == expectedMapped
        result.gptExplanation == explanation

        where:
        scenario           | score | explanation                    | expectedMapped
        "high confidence"  | 0.95  | "Excellent match found"        | true
        "medium confidence"| 0.75  | "Good match with minor issues" | true
        "low confidence"   | 0.45  | "Uncertain match"              | false
        "very low confidence"| 0.15| "No suitable match found"      | false
    }

    def "should handle GPT API errors gracefully"() {
        given: "a product for prediction"
        def product = createStandardProduct()

        and: "mock client throws API error"
        mockOpenAIClient.createCompletion(_) >> {
            throw FeignException.serviceUnavailable("OpenAI API unavailable", null, null)
        }

        when: "predicting with GPT error"
        def result = gptPredictionService.predictSingle(product)

        then: "should handle error gracefully"
        result != null
        result.predictionScore == 0.0
        result.mapped == false
        result.gptExplanation.contains("API error")
        1 * mockOpenAIClient.createCompletion(_)
    }

    def "should handle rate limiting with exponential backoff"() {
        given: "a product for prediction"
        def product = createStandardProduct()

        and: "mock client simulates rate limiting then success"
        def callCount = 0
        mockOpenAIClient.createCompletion(_) >> {
            callCount++
            if (callCount <= 2) {
                throw FeignException.tooManyRequests("Rate limit exceeded", null, null)
            }
            return createGPTResponse(0.80, "Success after retry")
        }

        when: "predicting with rate limiting"
        def result = gptPredictionService.predictSingleWithRetry(product)

        then: "should succeed after retries"
        result != null
        result.predictionScore == 0.80
        result.gptExplanation == "Success after retry"
        callCount == 3
    }

    def "should process batch predictions efficiently"() {
        given: "multiple products for batch processing"
        def products = (1..5).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "BATCH_SUPPLIER"
                channelId = "BATCH_CHANNEL"
                roomName = "Batch Room ${i}"
                rateName = "Batch Rate ${i}"
            }
        }

        and: "mock client returns batch responses"
        mockOpenAIClient.createCompletion(_) >> { args ->
            def prompt = args[0].prompt
            def roomNumber = prompt.find(/Room (\d+)/) { match, num -> num }
            return createGPTResponse(0.70 + (roomNumber as Integer) * 0.05, "Batch prediction ${roomNumber}")
        }

        when: "processing batch predictions"
        def results = gptPredictionService.predictBatch(products)

        then: "should process all products"
        results != null
        results.size() == 5
        results.every { it.predictionScore >= 0.70 }
        results.every { it.gptExplanation.startsWith("Batch prediction") }
        (5.._) * mockOpenAIClient.createCompletion(_)
    }

    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    def "should handle GPT API timeout gracefully"() {
        given: "a product for prediction"
        def product = createStandardProduct()

        and: "mock client simulates timeout"
        mockOpenAIClient.createCompletion(_) >> {
            Thread.sleep(10000) // Simulate long response time
            throw new RuntimeException("Request timeout")
        }

        when: "predicting with timeout"
        def result = gptPredictionService.predictSingleWithTimeout(product, 2000)

        then: "should handle timeout"
        result != null
        result.predictionScore == 0.0
        result.mapped == false
        result.gptExplanation.contains("timeout")
        1 * mockOpenAIClient.createCompletion(_)
    }

    def "should validate prompt engineering quality"() {
        given: "products with different characteristics"
        def luxuryProduct = new ChannelProductsDTO.Product().tap {
            roomName = "Presidential Suite with Ocean View"
            rateName = "Luxury Package with Spa Access"
            bedType = "KING"
            mealPlan = "ALL_INCLUSIVE"
            maxOccupancy = 4
        }

        def basicProduct = new ChannelProductsDTO.Product().tap {
            roomName = "Standard Room"
            rateName = "Basic Rate"
            bedType = "TWIN"
            mealPlan = "RO"
            maxOccupancy = 2
        }

        when: "generating prompts for different products"
        def luxuryPrompt = gptPredictionService.generatePredictionPrompt(luxuryProduct)
        def basicPrompt = gptPredictionService.generatePredictionPrompt(basicProduct)

        then: "prompts should be tailored to product characteristics"
        luxuryPrompt.contains("Presidential Suite")
        luxuryPrompt.contains("Luxury Package")
        luxuryPrompt.contains("ALL_INCLUSIVE")
        
        basicPrompt.contains("Standard Room")
        basicPrompt.contains("Basic Rate")
        basicPrompt.contains("RO")
        
        and: "prompts should have appropriate context"
        luxuryPrompt.length() > basicPrompt.length()
        luxuryPrompt.contains("luxury") || luxuryPrompt.contains("premium")
    }

    def "should handle malformed GPT responses"() {
        given: "a product for prediction"
        def product = createStandardProduct()

        and: "mock client returns malformed response"
        mockOpenAIClient.createCompletion(_) >> [
            choices: [
                [
                    text: "Invalid response format without proper JSON"
                ]
            ]
        ]

        when: "predicting with malformed response"
        def result = gptPredictionService.predictSingle(product)

        then: "should handle malformed response gracefully"
        result != null
        result.predictionScore >= 0.0
        result.predictionScore <= 1.0
        result.gptExplanation.contains("parsing error") || result.gptExplanation.contains("invalid format")
        1 * mockOpenAIClient.createCompletion(_)
    }

    def "should test concurrent GPT requests"() {
        given: "products for concurrent processing"
        def products = (1..3).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "CONCURRENT_SUPPLIER"
                channelId = "CONCURRENT_CHANNEL"
                roomName = "Concurrent Room ${i}"
                rateName = "Concurrent Rate ${i}"
            }
        }

        and: "mock client returns responses"
        mockOpenAIClient.createCompletion(_) >> { args ->
            Thread.sleep(100) // Simulate processing time
            return createGPTResponse(0.75, "Concurrent prediction")
        }

        when: "processing concurrent requests"
        def results = products.parallelStream()
            .map { product -> gptPredictionService.predictSingle(product) }
            .collect()

        then: "should handle concurrent requests"
        results.size() == 3
        results.every { it != null && it.predictionScore == 0.75 }
        (3.._) * mockOpenAIClient.createCompletion(_)
    }

    def "should validate token usage optimization"() {
        given: "a product with extensive details"
        def detailedProduct = new ChannelProductsDTO.Product().tap {
            roomName = "Luxury Presidential Suite with Ocean View and Private Balcony"
            rateName = "Premium All-Inclusive Package with Spa and Golf Access"
            bedType = "CALIFORNIA_KING"
            mealPlan = "ALL_INCLUSIVE_PREMIUM"
            maxOccupancy = 6
            extensions = [
                amenities: ["WiFi", "AC", "TV", "Minibar", "Safe", "Balcony", "Ocean View"],
                description: "Luxurious suite with premium amenities and services"
            ]
        }

        when: "generating optimized prompt"
        def prompt = gptPredictionService.generateOptimizedPrompt(detailedProduct)

        then: "should optimize token usage"
        prompt != null
        prompt.length() <= 2000 // Should be within reasonable token limit
        prompt.contains("Presidential Suite")
        prompt.contains("All-Inclusive")
        !prompt.contains("unnecessary repetition")
    }

    // Helper methods
    private ChannelProductsDTO.Product createStandardProduct() {
        new ChannelProductsDTO.Product().tap {
            supplierId = "STANDARD_SUPPLIER"
            channelId = "STANDARD_CHANNEL"
            roomName = "Standard Room"
            rateName = "Standard Rate"
            bedType = "QUEEN"
            mealPlan = "BB"
            maxOccupancy = 2
        }
    }

    private Map<String, Object> createGPTResponse(double score, String explanation) {
        [
            choices: [
                [
                    text: """
                    {
                        "prediction_score": ${score},
                        "confidence": "${score > 0.7 ? 'HIGH' : 'LOW'}",
                        "explanation": "${explanation}",
                        "mapped": ${score > 0.7}
                    }
                    """.trim()
                ]
            ],
            usage: [
                prompt_tokens: 150,
                completion_tokens: 50,
                total_tokens: 200
            ]
        ]
    }
}
