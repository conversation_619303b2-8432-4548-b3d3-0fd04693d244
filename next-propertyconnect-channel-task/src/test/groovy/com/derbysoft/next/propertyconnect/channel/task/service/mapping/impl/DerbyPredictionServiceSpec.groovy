package com.derbysoft.next.propertyconnect.channel.task.service.mapping.impl

import com.derbysoft.next.propertyconnect.channel.task.domain.dto.ChannelProductsDTO
import com.derbysoft.next.propertyconnect.channel.task.service.mapping.PredictionService
import spock.lang.Specification
import spock.lang.Shared
import spock.lang.Unroll
import spock.lang.Timeout

import java.util.concurrent.TimeUnit
import java.util.concurrent.ConcurrentHashMap

/**
 * Comprehensive Spock tests for DerbyPredictionService
 * Target: 80%+ line coverage for 609 lines of code
 * Tests prediction algorithms, scoring logic, nested classes, concurrent processing, and caching
 */
class DerbyPredictionServiceSpec extends Specification {

    @Shared
    DerbyPredictionService predictionService

    def setupSpec() {
        predictionService = new DerbyPredictionService()
    }

    def "should implement PredictionService interface"() {
        expect: "service implements the correct interface"
        predictionService instanceof PredictionService
    }

    def "should initialize with default configuration"() {
        when: "creating new service instance"
        def service = new DerbyPredictionService()

        then: "should have default settings"
        service != null
        service.isInitialized()
        service.getCacheSize() >= 0
        service.getDefaultThreshold() > 0.0
        service.getDefaultThreshold() <= 1.0
    }

    def "should predict single product successfully"() {
        given: "a single product for prediction"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Deluxe Room"
            rateName = "Best Available Rate"
            bedType = "KING"
            mealPlan = "BB"
            maxOccupancy = 2
        }

        when: "predicting the product"
        def result = predictionService.predictSingle(product)

        then: "should return prediction with score"
        result != null
        result.predictionScore >= 0.0
        result.predictionScore <= 1.0
        result.mapped == (result.predictionScore > predictionService.getDefaultThreshold())
        result.supplierId == product.supplierId
        result.channelId == product.channelId
    }

    def "should handle null product gracefully"() {
        when: "predicting null product"
        def result = predictionService.predictSingle(null)

        then: "should return null or empty result"
        result == null || result.predictionScore == 0.0
    }

    @Unroll
    def "should calculate prediction scores for different room types: #roomName"() {
        given: "product with specific room type"
        def product = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = roomName
            rateName = "Standard Rate"
            bedType = bedType
            maxOccupancy = occupancy
        }

        when: "predicting the product"
        def result = predictionService.predictSingle(product)

        then: "should return appropriate score based on room characteristics"
        result != null
        result.predictionScore >= expectedMinScore
        result.predictionScore <= expectedMaxScore

        where:
        roomName           | bedType | occupancy | expectedMinScore | expectedMaxScore
        "Deluxe Suite"     | "KING"  | 2         | 0.7             | 1.0
        "Standard Room"    | "QUEEN" | 2         | 0.5             | 0.8
        "Economy Room"     | "TWIN"  | 2         | 0.3             | 0.6
        "Presidential Suite" | "KING" | 4         | 0.8             | 1.0
        "Single Room"      | "SINGLE"| 1         | 0.4             | 0.7
    }

    def "should process batch predictions efficiently"() {
        given: "multiple products for batch prediction"
        def products = (1..100).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "SUPPLIER_001"
                channelId = "CHANNEL_001"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                bedType = i % 2 == 0 ? "KING" : "QUEEN"
                maxOccupancy = 2
            }
        }

        when: "processing batch predictions"
        def startTime = System.currentTimeMillis()
        def results = predictionService.predictBatch(products)
        def processingTime = System.currentTimeMillis() - startTime

        then: "should process all products efficiently"
        results != null
        results.size() == 100
        results.every { it.predictionScore >= 0.0 && it.predictionScore <= 1.0 }
        processingTime < 2000 // Should complete within 2 seconds
    }

    def "should test RoomTypeTranslator nested class"() {
        given: "room type translator instance"
        def translator = predictionService.getRoomTypeTranslator()

        when: "translating different room types"
        def deluxeScore = translator.translateRoomType("Deluxe Suite")
        def standardScore = translator.translateRoomType("Standard Room")
        def economyScore = translator.translateRoomType("Economy Room")

        then: "should return appropriate scores"
        translator != null
        deluxeScore > standardScore
        standardScore > economyScore
        deluxeScore >= 0.0 && deluxeScore <= 1.0
        standardScore >= 0.0 && standardScore <= 1.0
        economyScore >= 0.0 && economyScore <= 1.0
    }

    def "should test RatePlanTranslator nested class"() {
        given: "rate plan translator instance"
        def translator = predictionService.getRatePlanTranslator()

        when: "translating different rate plans"
        def barScore = translator.translateRatePlan("Best Available Rate")
        def advanceScore = translator.translateRatePlan("Advance Purchase")
        def nonRefundableScore = translator.translateRatePlan("Non-Refundable")

        then: "should return appropriate scores"
        translator != null
        barScore >= 0.0 && barScore <= 1.0
        advanceScore >= 0.0 && advanceScore <= 1.0
        nonRefundableScore >= 0.0 && nonRefundableScore <= 1.0
    }

    def "should handle caching mechanism correctly"() {
        given: "products for caching test"
        def product1 = new ChannelProductsDTO.Product().tap {
            supplierId = "CACHE_SUPPLIER"
            channelId = "CACHE_CHANNEL"
            roomName = "Cache Room"
            rateName = "Cache Rate"
        }
        def product2 = new ChannelProductsDTO.Product().tap {
            supplierId = "CACHE_SUPPLIER"
            channelId = "CACHE_CHANNEL"
            roomName = "Cache Room"
            rateName = "Cache Rate"
        }

        when: "predicting same product twice"
        def result1 = predictionService.predictSingle(product1)
        def result2 = predictionService.predictSingle(product2)

        then: "should use cache for second prediction"
        result1 != null
        result2 != null
        result1.predictionScore == result2.predictionScore
        predictionService.getCacheHitCount() > 0
    }

    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    def "should handle concurrent predictions safely"() {
        given: "products for concurrent processing"
        def products = (1..50).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "CONCURRENT_SUPPLIER"
                channelId = "CONCURRENT_CHANNEL"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
                bedType = "KING"
                maxOccupancy = 2
            }
        }

        when: "processing predictions concurrently"
        def results = products.parallelStream()
            .map { product -> predictionService.predictSingle(product) }
            .collect()

        then: "should handle concurrent access safely"
        results.size() == 50
        results.every { it != null }
        results.every { it.predictionScore >= 0.0 && it.predictionScore <= 1.0 }
    }

    def "should validate prediction algorithm consistency"() {
        given: "identical products"
        def product1 = createStandardProduct()
        def product2 = createStandardProduct()

        when: "predicting both products"
        def result1 = predictionService.predictSingle(product1)
        def result2 = predictionService.predictSingle(product2)

        then: "should return consistent results"
        result1.predictionScore == result2.predictionScore
        result1.mapped == result2.mapped
    }

    def "should handle edge cases in prediction scoring"() {
        given: "products with edge case characteristics"
        def emptyNameProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = ""
            rateName = ""
        }
        
        def nullFieldsProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = null
            rateName = null
            bedType = null
            mealPlan = null
        }

        when: "predicting edge case products"
        def emptyResult = predictionService.predictSingle(emptyNameProduct)
        def nullResult = predictionService.predictSingle(nullFieldsProduct)

        then: "should handle edge cases gracefully"
        emptyResult != null
        nullResult != null
        emptyResult.predictionScore >= 0.0
        nullResult.predictionScore >= 0.0
    }

    def "should test prediction confidence levels"() {
        given: "products with different confidence indicators"
        def highConfidenceProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Presidential Suite"
            rateName = "Best Available Rate"
            bedType = "KING"
            mealPlan = "BB"
            maxOccupancy = 4
        }
        
        def lowConfidenceProduct = new ChannelProductsDTO.Product().tap {
            supplierId = "SUPPLIER_001"
            channelId = "CHANNEL_001"
            roomName = "Unknown Room Type"
            rateName = "Special Rate"
            bedType = "UNKNOWN"
            mealPlan = "UNKNOWN"
            maxOccupancy = 0
        }

        when: "predicting products with different confidence levels"
        def highResult = predictionService.predictSingle(highConfidenceProduct)
        def lowResult = predictionService.predictSingle(lowConfidenceProduct)

        then: "should reflect confidence in scores"
        highResult.predictionScore > lowResult.predictionScore
        highResult.mapped == true
        lowResult.mapped == false
    }

    def "should validate scoring algorithm components"() {
        given: "service with scoring components"
        def service = predictionService

        when: "accessing scoring components"
        def roomTypeWeight = service.getRoomTypeWeight()
        def ratePlanWeight = service.getRatePlanWeight()
        def bedTypeWeight = service.getBedTypeWeight()
        def mealPlanWeight = service.getMealPlanWeight()

        then: "weights should be properly configured"
        roomTypeWeight > 0.0
        ratePlanWeight > 0.0
        bedTypeWeight > 0.0
        mealPlanWeight > 0.0
        (roomTypeWeight + ratePlanWeight + bedTypeWeight + mealPlanWeight) == 1.0
    }

    def "should test cache eviction policy"() {
        given: "service with limited cache size"
        predictionService.setCacheMaxSize(5)

        and: "more products than cache size"
        def products = (1..10).collect { i ->
            new ChannelProductsDTO.Product().tap {
                supplierId = "EVICTION_SUPPLIER"
                channelId = "EVICTION_CHANNEL"
                roomName = "Room ${i}"
                rateName = "Rate ${i}"
            }
        }

        when: "predicting all products"
        products.each { product ->
            predictionService.predictSingle(product)
        }

        then: "cache should not exceed max size"
        predictionService.getCacheSize() <= 5
        predictionService.getCacheEvictionCount() > 0
    }

    // Helper methods
    private ChannelProductsDTO.Product createStandardProduct() {
        new ChannelProductsDTO.Product().tap {
            supplierId = "STANDARD_SUPPLIER"
            channelId = "STANDARD_CHANNEL"
            roomName = "Standard Room"
            rateName = "Standard Rate"
            bedType = "QUEEN"
            mealPlan = "BB"
            maxOccupancy = 2
        }
    }
}
